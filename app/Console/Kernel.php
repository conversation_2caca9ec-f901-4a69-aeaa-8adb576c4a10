<?php

namespace App\Console;

use App\Console\Commands\RunIntegration;
use App\Console\Commands\RunSA1;
use App\Console\Commands\RunSB1;
use App\Console\Commands\RunSB2;
use App\Console\Commands\Runspx38940012;
use App\Console\Commands\RunTableTruncation;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        $schedule->command(RunTableTruncation::class)->dailyAt('03:04');

        $schedule->command(RunIntegration::class)->dailyAt('10:14');
        $schedule->command(RunSA1::class)->dailyAt('10:14');
        $schedule->command(RunSB1::class)->dailyAt('10:14');
        $schedule->command(RunSB2::class)->dailyAt('10:14');
        $schedule->command(RunIntegration::class)->dailyAt('10:29');
        $schedule->command(RunSA1::class)->dailyAt('10:29');
        $schedule->command(RunSB1::class)->dailyAt('10:29');
        $schedule->command(RunSB2::class)->dailyAt('10:29');
        $schedule->command(RunIntegration::class)->dailyAt('10:44');
        $schedule->command(RunSA1::class)->dailyAt('10:44');
        $schedule->command(RunSB1::class)->dailyAt('10:44');
        $schedule->command(RunSB2::class)->dailyAt('10:44');
        $schedule->command(RunIntegration::class)->dailyAt('10:59');
        $schedule->command(RunSA1::class)->dailyAt('10:59');
        $schedule->command(RunSB1::class)->dailyAt('10:59');
        $schedule->command(RunSB2::class)->dailyAt('10:59');

        $schedule->command(RunIntegration::class)->dailyAt('11:14');
        $schedule->command(RunSA1::class)->dailyAt('11:14');
        $schedule->command(RunSB1::class)->dailyAt('11:14');
        $schedule->command(RunSB2::class)->dailyAt('11:14');
        $schedule->command(RunIntegration::class)->dailyAt('11:34');
        $schedule->command(RunSA1::class)->dailyAt('11:34');
        $schedule->command(RunSB1::class)->dailyAt('11:34');
        $schedule->command(RunSB2::class)->dailyAt('11:34');
        $schedule->command(RunIntegration::class)->dailyAt('11:44');
        $schedule->command(RunSA1::class)->dailyAt('11:44');
        $schedule->command(RunSB1::class)->dailyAt('11:44');
        $schedule->command(RunSB2::class)->dailyAt('11:44');
        $schedule->command(RunIntegration::class)->dailyAt('11:59');
        $schedule->command(RunSA1::class)->dailyAt('11:59');
        $schedule->command(RunSB1::class)->dailyAt('11:59');
        $schedule->command(RunSB2::class)->dailyAt('11:59');

        $schedule->command(RunIntegration::class)->dailyAt('12:14');
        $schedule->command(RunSA1::class)->dailyAt('12:14');
        $schedule->command(RunSB1::class)->dailyAt('12:14');
        $schedule->command(RunSB2::class)->dailyAt('12:14');
        $schedule->command(RunIntegration::class)->dailyAt('12:29');
        $schedule->command(RunSA1::class)->dailyAt('12:29');
        $schedule->command(RunSB1::class)->dailyAt('12:29');
        $schedule->command(RunSB2::class)->dailyAt('12:29');
        $schedule->command(RunIntegration::class)->dailyAt('12:44');
        $schedule->command(RunSA1::class)->dailyAt('12:44');
        $schedule->command(RunSB1::class)->dailyAt('12:44');
        $schedule->command(RunSB2::class)->dailyAt('12:44');
        $schedule->command(RunIntegration::class)->dailyAt('12:59');
        $schedule->command(RunSA1::class)->dailyAt('12:59');
        $schedule->command(RunSB1::class)->dailyAt('12:59');
        $schedule->command(RunSB2::class)->dailyAt('12:59');

        $schedule->command(RunIntegration::class)->dailyAt('13:14');
        $schedule->command(RunSA1::class)->dailyAt('13:14');
        $schedule->command(RunSB1::class)->dailyAt('13:14');
        $schedule->command(RunSB2::class)->dailyAt('13:14');
        $schedule->command(RunIntegration::class)->dailyAt('13:34');
        $schedule->command(RunSA1::class)->dailyAt('13:34');
        $schedule->command(RunSB1::class)->dailyAt('13:34');
        $schedule->command(RunSB2::class)->dailyAt('13:34');
        $schedule->command(RunIntegration::class)->dailyAt('13:44');
        $schedule->command(RunSA1::class)->dailyAt('13:44');
        $schedule->command(RunSB1::class)->dailyAt('13:44');
        $schedule->command(RunSB2::class)->dailyAt('13:44');
        $schedule->command(RunIntegration::class)->dailyAt('13:59');
        $schedule->command(RunSA1::class)->dailyAt('13:59');
        $schedule->command(RunSB1::class)->dailyAt('13:59');
        $schedule->command(RunSB2::class)->dailyAt('13:59');

        $schedule->command(RunIntegration::class)->dailyAt('14:14');
        $schedule->command(RunSA1::class)->dailyAt('14:14');
        $schedule->command(RunSB1::class)->dailyAt('14:14');
        $schedule->command(RunSB2::class)->dailyAt('14:14');
        $schedule->command(RunIntegration::class)->dailyAt('14:29');
        $schedule->command(RunSA1::class)->dailyAt('14:29');
        $schedule->command(RunSB1::class)->dailyAt('14:29');
        $schedule->command(RunSB2::class)->dailyAt('14:29');
        $schedule->command(RunIntegration::class)->dailyAt('14:44');
        $schedule->command(RunSA1::class)->dailyAt('14:44');
        $schedule->command(RunSB1::class)->dailyAt('14:44');
        $schedule->command(RunSB2::class)->dailyAt('14:44');
        $schedule->command(RunIntegration::class)->dailyAt('14:59');
        $schedule->command(RunSA1::class)->dailyAt('14:59');
        $schedule->command(RunSB1::class)->dailyAt('14:59');
        $schedule->command(RunSB2::class)->dailyAt('14:59');

        $schedule->command(RunIntegration::class)->dailyAt('15:14');
        $schedule->command(RunSA1::class)->dailyAt('15:14');
        $schedule->command(RunSB1::class)->dailyAt('15:14');
        $schedule->command(RunSB2::class)->dailyAt('15:14');
        $schedule->command(RunIntegration::class)->dailyAt('15:29');
        $schedule->command(RunSA1::class)->dailyAt('15:29');
        $schedule->command(RunSB1::class)->dailyAt('15:29');
        $schedule->command(RunSB2::class)->dailyAt('15:29');
        $schedule->command(RunIntegration::class)->dailyAt('15:44');
        $schedule->command(RunSA1::class)->dailyAt('15:44');
        $schedule->command(RunSB1::class)->dailyAt('15:44');
        $schedule->command(RunSB2::class)->dailyAt('15:44');
        $schedule->command(RunIntegration::class)->dailyAt('15:59');
        $schedule->command(RunSA1::class)->dailyAt('15:59');
        $schedule->command(RunSB1::class)->dailyAt('15:59');
        $schedule->command(RunSB2::class)->dailyAt('15:59');

        $schedule->command(RunIntegration::class)->dailyAt('16:14');
        $schedule->command(RunSA1::class)->dailyAt('16:14');
        $schedule->command(RunSB1::class)->dailyAt('16:14');
        $schedule->command(RunSB2::class)->dailyAt('16:14');
        $schedule->command(RunIntegration::class)->dailyAt('16:29');
        $schedule->command(RunSA1::class)->dailyAt('16:29');
        $schedule->command(RunSB1::class)->dailyAt('16:29');
        $schedule->command(RunSB2::class)->dailyAt('16:29');
        $schedule->command(RunIntegration::class)->dailyAt('16:44');
        $schedule->command(RunSA1::class)->dailyAt('16:44');
        $schedule->command(RunSB1::class)->dailyAt('16:44');
        $schedule->command(RunSB2::class)->dailyAt('16:44');

        $schedule->command(RunIntegration::class)->dailyAt('17:04');
        $schedule->command(RunSA1::class)->dailyAt('17:04');
        $schedule->command(RunSB1::class)->dailyAt('17:04');
        $schedule->command(RunSB2::class)->dailyAt('17:04');
        $schedule->command(RunIntegration::class)->dailyAt('17:17');
        $schedule->command(RunSA1::class)->dailyAt('17:17');
        $schedule->command(RunSB1::class)->dailyAt('17:17');
        $schedule->command(RunSB2::class)->dailyAt('17:17');
        $schedule->command(RunIntegration::class)->dailyAt('17:29');
        $schedule->command(RunSA1::class)->dailyAt('17:29');
        $schedule->command(RunSB1::class)->dailyAt('17:29');
        $schedule->command(RunSB2::class)->dailyAt('17:29');
        $schedule->command(RunIntegration::class)->dailyAt('17:44');
        $schedule->command(RunSA1::class)->dailyAt('17:44');
        $schedule->command(RunSB1::class)->dailyAt('17:44');
        $schedule->command(RunSB2::class)->dailyAt('17:44');

        $schedule->command(RunIntegration::class)->dailyAt('18:04');
        $schedule->command(RunSA1::class)->dailyAt('18:04');
        $schedule->command(RunSB1::class)->dailyAt('18:04');
        $schedule->command(RunSB2::class)->dailyAt('18:04');
        $schedule->command(RunIntegration::class)->dailyAt('18:14');
        $schedule->command(RunSA1::class)->dailyAt('18:14');
        $schedule->command(RunSB1::class)->dailyAt('18:14');
        $schedule->command(RunSB2::class)->dailyAt('18:14');
        $schedule->command(RunIntegration::class)->dailyAt('18:29');
        $schedule->command(RunSA1::class)->dailyAt('18:29');
        $schedule->command(RunSB1::class)->dailyAt('18:29');
        $schedule->command(RunSB2::class)->dailyAt('18:29');
        $schedule->command(RunIntegration::class)->dailyAt('18:44');
        $schedule->command(RunSA1::class)->dailyAt('18:44');
        $schedule->command(RunSB1::class)->dailyAt('18:44');
        $schedule->command(RunSB2::class)->dailyAt('18:44');

        $schedule->command(RunIntegration::class)->dailyAt('19:04');
        $schedule->command(RunSA1::class)->dailyAt('19:04');
        $schedule->command(RunSB1::class)->dailyAt('19:04');
        $schedule->command(RunSB2::class)->dailyAt('19:04');
        $schedule->command(RunIntegration::class)->dailyAt('19:14');
        $schedule->command(RunSA1::class)->dailyAt('19:14');
        $schedule->command(RunSB1::class)->dailyAt('19:14');
        $schedule->command(RunSB2::class)->dailyAt('19:14');
        $schedule->command(RunIntegration::class)->dailyAt('19:29');
        $schedule->command(RunSA1::class)->dailyAt('19:29');
        $schedule->command(RunSB1::class)->dailyAt('19:29');
        $schedule->command(RunSB2::class)->dailyAt('19:29');
        $schedule->command(RunIntegration::class)->dailyAt('19:44');
        $schedule->command(RunSA1::class)->dailyAt('19:44');
        $schedule->command(RunSB1::class)->dailyAt('19:44');
        $schedule->command(RunSB2::class)->dailyAt('19:44');
        $schedule->command(RunIntegration::class)->dailyAt('19:59');
        $schedule->command(RunSA1::class)->dailyAt('19:59');
        $schedule->command(RunSB1::class)->dailyAt('19:59');
        $schedule->command(RunSB2::class)->dailyAt('19:59');

        $schedule->command(RunIntegration::class)->dailyAt('20:14');
        $schedule->command(RunSA1::class)->dailyAt('20:14');
        $schedule->command(RunSB1::class)->dailyAt('20:14');
        $schedule->command(RunSB2::class)->dailyAt('20:14');
        $schedule->command(RunIntegration::class)->dailyAt('20:29');
        $schedule->command(RunSA1::class)->dailyAt('20:29');
        $schedule->command(RunSB1::class)->dailyAt('20:29');
        $schedule->command(RunSB2::class)->dailyAt('20:29');
        $schedule->command(RunIntegration::class)->dailyAt('20:44');
        $schedule->command(RunSA1::class)->dailyAt('20:44');
        $schedule->command(RunSB1::class)->dailyAt('20:44');
        $schedule->command(RunSB2::class)->dailyAt('20:44');
        $schedule->command(RunIntegration::class)->dailyAt('20:59');
        $schedule->command(RunSA1::class)->dailyAt('20:59');
        $schedule->command(RunSB1::class)->dailyAt('20:59');
        $schedule->command(RunSB2::class)->dailyAt('20:59');

        $schedule->command(RunIntegration::class)->dailyAt('21:14');
        $schedule->command(RunSA1::class)->dailyAt('21:14');
        $schedule->command(RunSB1::class)->dailyAt('21:14');
        $schedule->command(RunSB2::class)->dailyAt('21:14');
        $schedule->command(RunIntegration::class)->dailyAt('21:34');
        $schedule->command(RunSA1::class)->dailyAt('21:34');
        $schedule->command(RunSB1::class)->dailyAt('21:34');
        $schedule->command(RunSB2::class)->dailyAt('21:34');
        $schedule->command(RunIntegration::class)->dailyAt('21:44');
        $schedule->command(RunSA1::class)->dailyAt('21:44');
        $schedule->command(RunSB1::class)->dailyAt('21:44');
        $schedule->command(RunSB2::class)->dailyAt('21:44');
        $schedule->command(RunIntegration::class)->dailyAt('21:59');
        $schedule->command(RunSA1::class)->dailyAt('21:59');
        $schedule->command(RunSB1::class)->dailyAt('21:59');
        $schedule->command(RunSB2::class)->dailyAt('21:59');

        $schedule->command(RunIntegration::class)->dailyAt('22:14');
        $schedule->command(RunSA1::class)->dailyAt('22:14');
        $schedule->command(RunSB1::class)->dailyAt('22:14');
        $schedule->command(RunSB2::class)->dailyAt('22:14');
        $schedule->command(RunIntegration::class)->dailyAt('22:29');
        $schedule->command(RunSA1::class)->dailyAt('22:29');
        $schedule->command(RunSB1::class)->dailyAt('22:29');
        $schedule->command(RunSB2::class)->dailyAt('22:29');
        $schedule->command(RunIntegration::class)->dailyAt('22:44');
        $schedule->command(RunSA1::class)->dailyAt('22:44');
        $schedule->command(RunSB1::class)->dailyAt('22:44');
        $schedule->command(RunSB2::class)->dailyAt('22:44');
        $schedule->command(RunIntegration::class)->dailyAt('22:59');
        $schedule->command(RunSA1::class)->dailyAt('22:59');
        $schedule->command(RunSB1::class)->dailyAt('22:59');
        $schedule->command(RunSB2::class)->dailyAt('22:59');

        $schedule->command(RunIntegration::class)->dailyAt('23:14');
        $schedule->command(RunSA1::class)->dailyAt('23:14');
        $schedule->command(RunSB1::class)->dailyAt('23:14');
        $schedule->command(RunSB2::class)->dailyAt('23:14');
        $schedule->command(RunIntegration::class)->dailyAt('23:34');
        $schedule->command(RunSA1::class)->dailyAt('23:34');
        $schedule->command(RunSB1::class)->dailyAt('23:34');
        $schedule->command(RunSB2::class)->dailyAt('23:34');
        $schedule->command(RunIntegration::class)->dailyAt('23:44');
        $schedule->command(RunSA1::class)->dailyAt('23:44');
        $schedule->command(RunSB1::class)->dailyAt('23:44');
        $schedule->command(RunSB2::class)->dailyAt('23:44');
        $schedule->command(RunIntegration::class)->dailyAt('23:59');
        $schedule->command(RunSA1::class)->dailyAt('23:59');
        $schedule->command(RunSB1::class)->dailyAt('23:59');
        $schedule->command(RunSB2::class)->dailyAt('23:59');

        $schedule->command(RunIntegration::class)->dailyAt('00:14');
        $schedule->command(RunSA1::class)->dailyAt('00:14');
        $schedule->command(RunSB1::class)->dailyAt('00:14');
        $schedule->command(RunSB2::class)->dailyAt('00:14');
        $schedule->command(RunIntegration::class)->dailyAt('00:29');
        $schedule->command(RunSA1::class)->dailyAt('00:29');
        $schedule->command(RunSB1::class)->dailyAt('00:29');
        $schedule->command(RunSB2::class)->dailyAt('00:29');
        $schedule->command(RunIntegration::class)->dailyAt('00:44');
        $schedule->command(RunSA1::class)->dailyAt('00:44');
        $schedule->command(RunSB1::class)->dailyAt('00:44');
        $schedule->command(RunSB2::class)->dailyAt('00:44');
        $schedule->command(RunIntegration::class)->dailyAt('00:59');
        $schedule->command(RunSA1::class)->dailyAt('00:59');
        $schedule->command(RunSB1::class)->dailyAt('00:59');
        $schedule->command(RunSB2::class)->dailyAt('00:59');

        $schedule->command(RunIntegration::class)->dailyAt('01:14');
        $schedule->command(RunSA1::class)->dailyAt('01:14');
        $schedule->command(RunSB1::class)->dailyAt('01:14');
        $schedule->command(RunSB2::class)->dailyAt('01:14');
        $schedule->command(RunIntegration::class)->dailyAt('01:29');
        $schedule->command(RunSA1::class)->dailyAt('01:29');
        $schedule->command(RunSB1::class)->dailyAt('01:29');
        $schedule->command(RunSB2::class)->dailyAt('01:29');
        $schedule->command(RunIntegration::class)->dailyAt('01:44');
        $schedule->command(RunSA1::class)->dailyAt('01:44');
        $schedule->command(RunSB1::class)->dailyAt('01:44');
        $schedule->command(RunSB2::class)->dailyAt('01:44');
        $schedule->command(RunIntegration::class)->dailyAt('01:59');
        $schedule->command(RunSA1::class)->dailyAt('01:59');
        $schedule->command(RunSB1::class)->dailyAt('01:59');
        $schedule->command(RunSB2::class)->dailyAt('01:59');

        $schedule->command(RunIntegration::class)->dailyAt('02:14');
        $schedule->command(RunSA1::class)->dailyAt('02:14');
        $schedule->command(RunSB1::class)->dailyAt('02:14');
        $schedule->command(RunSB2::class)->dailyAt('02:14');
        $schedule->command(RunIntegration::class)->dailyAt('02:34');
        $schedule->command(RunSA1::class)->dailyAt('02:34');
        $schedule->command(RunSB1::class)->dailyAt('02:34');
        $schedule->command(RunSB2::class)->dailyAt('02:34');
        $schedule->command(RunIntegration::class)->dailyAt('02:44');
        $schedule->command(RunSA1::class)->dailyAt('02:44');
        $schedule->command(RunSB1::class)->dailyAt('02:44');
        $schedule->command(RunSB2::class)->dailyAt('02:44');
        $schedule->command(RunIntegration::class)->dailyAt('02:59');
        $schedule->command(RunSA1::class)->dailyAt('02:59');
        $schedule->command(RunSB1::class)->dailyAt('02:59');
        $schedule->command(RunSB2::class)->dailyAt('02:59');
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
