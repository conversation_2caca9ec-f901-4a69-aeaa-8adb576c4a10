<?php

namespace App\Console\Commands;

use App\Jobs\ErpFlex\RunDataUpdate;
use App\Models\IntegrationSetting;
use App\Models\Tenant;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;

class RunSB2 extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:run-sb2 {tenant?} {force?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Run the integrations.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $tenants = Tenant::query()
            ->when($this->argument('tenant'), fn (Builder $query): Builder => $query->where('id', $this->argument('tenant')))
            ->get();

        tenancy()->runForMultiple($tenants, function () {
            /** @var \App\Models\IntegrationSetting $integrationSetting */
            $integrationSetting = IntegrationSetting::first();

            if (!$integrationSetting->SB2) {
                return;
            }

            RunDataUpdate::dispatchSync('SB2', $this->argument('force') ?? false);
        });
    }
}
