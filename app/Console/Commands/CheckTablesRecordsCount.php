<?php

namespace App\Console\Commands;

use App\Models\IntegrationCutoff;
use App\Models\IntegrationSetting;
use App\Models\Tenant;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;

class CheckTablesRecordsCount extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:check-tables-records-count {tenant?} {table_name?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $tenants = Tenant::query()
            ->when($this->argument('tenant'), fn(Builder $query): Builder => $query->where('id', $this->argument('tenant')))
            ->get();

        tenancy()->runForMultiple($tenants, function () {
            /** @var \App\Models\IntegrationSetting $integrationSetting */
            $integrationSetting = IntegrationSetting::first();

            /** @var \App\Models\IntegrationCutoff $integrationCutoff */
            $integrationCutoff = IntegrationCutoff::first();

            collect($integrationSetting->getAttributes())
                ->except(['id', 'created_at', 'updated_at'])
                ->filter(fn(bool $value, string $key): bool => $value)
                ->keys()
                ->each(function (string $tableName) use ($integrationCutoff): void {
                    if (!is_null($this->argument('table_name')) && $tableName !== $this->argument('table_name')) {
                        return;
                    }

                    $erpFlexClass = '\\App\\Models\\ErpFlex\\' . $tableName;
                    $class = '\\App\\Models\\' . $tableName;

                    $erpFlexCount = $erpFlexClass::query()->get()->count();
                    $integrationCount = $class::query()->get()->count();

                    if ($erpFlexCount === $integrationCount) {
                        return;
                    }

                    $integrationCutoff->update(["last_{$tableName}_update" => null]);
                });
        });
    }
}
