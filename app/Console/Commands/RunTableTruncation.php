<?php

namespace App\Console\Commands;

use App\Jobs\ErpFlex\RunDataUpdate;
use App\Models\IntegrationSetting;
use App\Models\Tenant;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;

class RunTableTruncation extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:run-table-truncation {tenant?} {force?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Run the table truncations.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $tenants = Tenant::query()
            ->when($this->argument('tenant'), fn (Builder $query): Builder => $query->where('id', $this->argument('tenant')))
            ->get();

        tenancy()->runForMultiple($tenants, function () {
            /** @var \App\Models\IntegrationSetting $integrationSetting */
            $integrationSetting = IntegrationSetting::first();

            collect($integrationSetting->getAttributes())
                ->except(['id', 'created_at', 'updated_at'])
                ->filter(fn (bool $value, string $key): bool => $value)
                ->keys()
                ->each(function (string $tableName): void {
                    if (str_starts_with($tableName, 'spx')) {
                        $queue = 'spx';
                    } else {
                        $queue = match ($tableName) {
                            'SB2_SBW' => 'sb2_sbw',
                            'SD1', 'SD2', 'SD3', 'SD4' => 'sd',
                            'SG0', 'SG1' => 'sg',
                            'SC2', 'SC3', 'SC4', 'SC5', 'SC6', 'SC7' => 'sc',
                            'SF1', 'SF2', 'SF3' => 'sf',
                            default => 'default'
                        };
                    }

                    RunDataUpdate::dispatchSync($tableName, true, true);
                });
        });
    }
}
