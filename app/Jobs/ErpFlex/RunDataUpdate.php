<?php

namespace App\Jobs\ErpFlex;

use App\Models\IntegrationCutoff;
use App\Models\IntegrationSetting;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class RunDataUpdate implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(public string $tableName, public bool $force = false, public bool $truncate = false) {}

    public function handle(): void
    {
        /** @var \App\Models\IntegrationSetting $integrationSetting */
        $integrationSetting = IntegrationSetting::first();

        if (!$integrationSetting->{$this->tableName}) {
            return;
        }

        /** @var \App\Models\IntegrationCutoff $integrationCutoff */
        $integrationCutoff = IntegrationCutoff::first();

        $executionStart = now();

        if (!$this->force && !is_null($integrationCutoff->{"last_{$this->tableName}_update"})) {
            $lastUpdateAt = Carbon::parse($integrationCutoff->{"last_{$this->tableName}_update"});
        } else {
            $lastUpdateAt = null;
            $this->force = true;
        }

        if (in_array($this->tableName, ['SB2_SBW', 'SBL'])) {
            $this->force = true;
        }

        $erpFlexClass = '\\App\\Models\\ErpFlex\\' . $this->tableName;
        $class = '\\App\\Models\\' . $this->tableName;

        $destandardizedTables = [
            'apr_assinaturas',
            'apr_processos',
            'spx38940001',
            'spx38940005',
            'spx38940008',
            'spx38940012',
            'spx38940016',
            'spx38940017',
            'spx38940018',
            'spx38940019',
            'spx38940027',
            'spx38940031',
            'spx38940033',
            'spx38940035',
            'spx38940036',
            'spx38940039',
            'spx38940040',
            'spx38940043',
        ];

        $nonIDEA1Tables = [
            'CFO',
            'SAE',
        ];

        if ($this->truncate) {
            // DB::statement("truncate table $this->tableName");
            $this->recreateTable();
        }

        $i = 0;

        while (true) {
            $query = DB::connection('erpflex_mysql')
                ->table($this->tableName)
                ->when(
                    !in_array($this->tableName, $nonIDEA1Tables) && !in_array($this->tableName, $destandardizedTables),
                    fn($query) => $query->where($this->tableName . '_IDEA1', ((int) tenant()->erp_flex['company_id']))
                )
                ->when(
                    in_array($this->tableName, ['apr_assinaturas', 'apr_processos']),
                    fn($query) => $query->where('id_ea1', ((int) tenant()->erp_flex['company_id']))
                )
                ->when(!$this->force, function ($query) use ($lastUpdateAt, $destandardizedTables) {
                    return $query
                        ->when(!in_array($this->tableName, $destandardizedTables), function ($query) use ($lastUpdateAt) {
                            return $query->where(function ($query) use ($lastUpdateAt) {
                                return $query
                                    ->when(!in_array($this->tableName, ['SE4']), fn($query) => $query->where($this->tableName . '_DT_INC', ">=", $lastUpdateAt))
                                    ->when(!in_array($this->tableName, ['CRM_EMP', 'CRM_OP', 'CRM_PR', 'SE4']), fn($query) => $query->orWhere($this->tableName . '_DT_ALT', ">=", $lastUpdateAt))
                                    ->when($this->tableName === 'SB2', fn($query) => $query->orWhere($this->tableName . '_DT_ALT_EST', '>=', $lastUpdateAt));
                            });
                        })
                        ->when($this->tableName === 'spx38940012', function ($query) {
                            return $query->where('cpo003', '>=', now()->subMonths(6)->format('Y-m-d H:i:s'));
                        });
                });

            $results = $query->skip($i)->take(1000)->get();

            $count = $results->count();

            $this->handleGroup($results, $erpFlexClass, $class, $destandardizedTables, $nonIDEA1Tables);

            if ($count < 1000) {
                break;
            }

            $i += 1000;
        }

        $integrationCutoff->{"last_{$this->tableName}_update"} = $executionStart;
        $integrationCutoff->save();
    }

    protected function handleGroup(
        Collection $group,
        string $erpFlexClass,
        string $class,
        array $destandardizedTables,
        array $nonIDEA1Tables
    ): void {
        $group->each(function ($erpFlexModel) use ($erpFlexClass, $class, $destandardizedTables, $nonIDEA1Tables): void {
            $erpFlexModelData = json_decode(json_encode($erpFlexModel), true);

            if (in_array($this->tableName, $destandardizedTables)) {
                $primaryKey = 'id';

                if ($this->tableName === 'spx38940001') {
                    $erpFlexModelData['cpo007'] = Carbon::parse($erpFlexModelData['cpo007'])->lt(now()->startOfMillennium()) ? '1970-01-01 00:00:00' : $erpFlexModelData['cpo007'];
                    $erpFlexModelData['cpo010'] = Carbon::parse($erpFlexModelData['cpo010'])->lt(now()->startOfMillennium()) ? '1970-01-01 00:00:00' : $erpFlexModelData['cpo010'];
                }

                if ($this->tableName === 'spx38940005') {
                    $erpFlexModelData['cpo001'] = Carbon::parse($erpFlexModelData['cpo001'])->lt(now()->startOfMillennium()) ? '1970-01-01 00:00:00' : $erpFlexModelData['cpo001'];
                    $erpFlexModelData['cpo015'] = Carbon::parse($erpFlexModelData['cpo015'])->lt(now()->startOfMillennium()) ? '1970-01-01 00:00:00' : $erpFlexModelData['cpo015'];
                }

                if ($this->tableName === 'spx38940008') {
                    $erpFlexModelData['cpo005'] = Carbon::parse($erpFlexModelData['cpo005'])->lt(now()->startOfMillennium()) ? '1970-01-01 00:00:00' : $erpFlexModelData['cpo005'];
                }

                if ($this->tableName === 'spx38940012') {
                    $erpFlexModelData['cpo003'] = Carbon::parse($erpFlexModelData['cpo003'])->lt(now()->startOfMillennium()) ? '1970-01-01 00:00:00' : $erpFlexModelData['cpo003'];
                    $erpFlexModelData['cpo007'] = Carbon::parse($erpFlexModelData['cpo007'])->lt(now()->startOfMillennium()) ? '1970-01-01 00:00:00' : $erpFlexModelData['cpo007'];
                }

                if ($this->tableName === 'spx38940017') {
                    $erpFlexModelData['cpo010'] = isset($erpFlexModelData['cpo010']) && !is_null($erpFlexModelData['cpo010'])
                        ? (Carbon::parse($erpFlexModelData['cpo010'])->lt(now()->startOfMillennium()) ? '1970-01-01 00:00:00' : $erpFlexModelData['cpo010'])
                        : null;
                    $erpFlexModelData['cpo024'] = isset($erpFlexModelData['cpo024']) && !is_null($erpFlexModelData['cpo024'])
                        ? (Carbon::parse($erpFlexModelData['cpo024'])->lt(now()->startOfMillennium()) ? '1970-01-01 00:00:00' : $erpFlexModelData['cpo024'])
                        : null;
                    $erpFlexModelData['cpo034'] = isset($erpFlexModelData['cpo034']) && !is_null($erpFlexModelData['cpo034'])
                        ? (Carbon::parse($erpFlexModelData['cpo034'])->lt(now()->startOfMillennium()) ? '1970-01-01 00:00:00' : $erpFlexModelData['cpo034'])
                        : null;

                    unset($erpFlexModelData['cpo027']);
                    unset($erpFlexModelData['cpo028']);
                }

                if ($this->tableName === 'spx38940031') {
                    $erpFlexModelData['cpo001'] = Carbon::parse($erpFlexModelData['cpo001'])->lt(now()->startOfMillennium()) ? '1970-01-01 00:00:00' : $erpFlexModelData['cpo001'];
                }

                if ($this->tableName === 'spx38940033') {
                    $erpFlexModelData['cpo001'] = Carbon::parse($erpFlexModelData['cpo001'])->lt(now()->startOfMillennium()) ? '1970-01-01 00:00:00' : $erpFlexModelData['cpo001'];
                    $erpFlexModelData['cpo013'] = Carbon::parse($erpFlexModelData['cpo013'])->lt(now()->startOfMillennium()) ? '1970-01-01 00:00:00' : $erpFlexModelData['cpo013'];
                    $erpFlexModelData['cpo014'] = Carbon::parse($erpFlexModelData['cpo014'])->lt(now()->startOfMillennium()) ? '1970-01-01 00:00:00' : $erpFlexModelData['cpo014'];
                }

                if ($this->tableName === 'spx38940035') {
                    $erpFlexModelData['cpo001'] = Carbon::parse($erpFlexModelData['cpo001'])->lt(now()->startOfMillennium()) ? '1970-01-01 00:00:00' : $erpFlexModelData['cpo001'];
                    $erpFlexModelData['cpo008'] = Carbon::parse($erpFlexModelData['cpo008'])->lt(now()->startOfMillennium()) ? '1970-01-01 00:00:00' : $erpFlexModelData['cpo008'];
                    unset($erpFlexModelData['cpo009']);
                }

                if ($this->tableName === 'spx38940036') {
                    $erpFlexModelData['cpo003'] = Carbon::parse($erpFlexModelData['cpo003'])->lt(now()->startOfMillennium()) ? '1970-01-01 00:00:00' : $erpFlexModelData['cpo003'];
                }

                $class::updateOrCreate([
                    $primaryKey => $erpFlexModel->$primaryKey,
                ], $erpFlexModelData);

                return;
            }

            if (!in_array($this->tableName, $nonIDEA1Tables) && ((int) $erpFlexModelData[$this->tableName . '_IDEA1']) !== ((int) tenant()->erp_flex['company_id'])) {
                return;
            }

            if ((int) $erpFlexModelData[$this->tableName . '_ID'] === 0) {
                return;
            }

            unset($erpFlexModelData[$this->tableName . '_ID']);

            if (isset($erpFlexModelData[$erpFlexClass::CREATED_AT]) && !is_null($erpFlexClass::CREATED_AT) && Carbon::parse($erpFlexModelData[$erpFlexClass::CREATED_AT])->lt(now()->startOfMillennium())) {
                $erpFlexModelData[$erpFlexClass::CREATED_AT] = '1970-01-01 00:00:00';
            }

            if (isset($erpFlexModelData[$erpFlexClass::UPDATED_AT]) && !is_null($erpFlexClass::UPDATED_AT) && Carbon::parse($erpFlexModelData[$erpFlexClass::UPDATED_AT])->lt(now()->startOfMillennium())) {
                $erpFlexModelData[$erpFlexClass::UPDATED_AT] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'EA1' && Carbon::parse($erpFlexModelData['EA1_DtExpiracao'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['EA1_DtExpiracao'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'EA2' && Carbon::parse($erpFlexModelData['EA2_DT_ULTLOGON'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['EA2_DT_ULTLOGON'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'IE2' && Carbon::parse($erpFlexModelData['IE2_Emissao'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['IE2_Emissao'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SA1' && Carbon::parse($erpFlexModelData['SA1_DtNasc'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SA1_DtNasc'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SA1' && Carbon::parse($erpFlexModelData['SA1_DtVenctoCred'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SA1_DtVenctoCred'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SA1' && Carbon::parse($erpFlexModelData['SA1_DT_INC_LV'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SA1_DT_INC_LV'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SA1' && Carbon::parse($erpFlexModelData['SA1_DT_ALT_LV'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SA1_DT_ALT_LV'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SB1') {
                unset($erpFlexModelData['XXX_IDTemp']);
            }

            if ($this->tableName === 'SB1' && Carbon::parse($erpFlexModelData['SB1_DtValidade'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SB1_DtValidade'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SB2' && Carbon::parse($erpFlexModelData['SB2_DtValidade'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SB2_DtValidade'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SB2' && Carbon::parse($erpFlexModelData['SB2_DtUltimoAjuste'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SB2_DtUltimoAjuste'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SB2' && Carbon::parse($erpFlexModelData['SB2_DtUltimoCalculoPP'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SB2_DtUltimoCalculoPP'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SB2' && Carbon::parse($erpFlexModelData['SB2_DtGeracaoFCI'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SB2_DtGeracaoFCI'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SBL' && Carbon::parse($erpFlexModelData['SBL_DtValidade'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SBL_DtValidade'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SBL' && Carbon::parse($erpFlexModelData['SBL_DtFabricacao'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SBL_DtFabricacao'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SBL' && Carbon::parse($erpFlexModelData['SBL_DtEntrada'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SBL_DtEntrada'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SC2' && Carbon::parse($erpFlexModelData['SC2_Emissao'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SC2_Emissao'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SC2' && Carbon::parse($erpFlexModelData['SC2_Previsao'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SC2_Previsao'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SC2' && Carbon::parse($erpFlexModelData['SC2_DataProd'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SC2_DataProd'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SC3' && Carbon::parse($erpFlexModelData['SC3_Emissao'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SC3_Emissao'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SC3' && Carbon::parse($erpFlexModelData['SC3_DtPrevCom'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SC3_DtPrevCom'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SC4' && Carbon::parse($erpFlexModelData['SC4_DataEntrega'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SC4_DataEntrega'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SC5' && Carbon::parse($erpFlexModelData['SC5_Emissao'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SC5_Emissao'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SC5' && Carbon::parse($erpFlexModelData['SC5_DtIniExec'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SC5_DtIniExec'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SC5' && Carbon::parse($erpFlexModelData['SC5_DtFinExec'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SC5_DtFinExec'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SC5' && Carbon::parse($erpFlexModelData['SC5_DtVencto'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SC5_DtVencto'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SC5' && Carbon::parse($erpFlexModelData['SC5_DtPrevFat'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SC5_DtPrevFat'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SC5' && Carbon::parse($erpFlexModelData['SC5_DtAprovCli'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SC5_DtAprovCli'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SC5' && Carbon::parse($erpFlexModelData['SC5_HrBloqueio'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SC5_HrBloqueio'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SC6' && Carbon::parse($erpFlexModelData['SC6_DataEntrega'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SC6_DataEntrega'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SC6' && Carbon::parse($erpFlexModelData['SC6_DtSeparacao'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SC6_DtSeparacao'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SC7' && Carbon::parse($erpFlexModelData['SC7_Vencto'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SC7_Vencto'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SD1' && Carbon::parse($erpFlexModelData['SD1_DtValidade'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SD1_DtValidade'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SD1' && Carbon::parse($erpFlexModelData['SD1_dFab'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SD1_dFab'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SD1' && Carbon::parse($erpFlexModelData['SD1_dVal'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SD1_dVal'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SD1' && Carbon::parse($erpFlexModelData['SD1_DtRefContrato'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SD1_DtRefContrato'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SD2' && Carbon::parse($erpFlexModelData['SD2_dFab'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SD2_dFab'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SD2' && Carbon::parse($erpFlexModelData['SD2_dVal'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SD2_dVal'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SD2' && Carbon::parse($erpFlexModelData['SD2_DtRefContrato'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SD2_DtRefContrato'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SD2' && Carbon::parse($erpFlexModelData['SD2_DataDespacho'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SD2_DataDespacho'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SD2' && Carbon::parse($erpFlexModelData['SD2_DtEntrega'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SD2_DtEntrega'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SD2' && !is_null($erpFlexModelData['SD2_DataDespacho']) && explode('-', $erpFlexModelData['SD2_DataDespacho'])[1] === '00') {
                $erpFlexModelData['SD2_DataDespacho'] = Carbon::parse($erpFlexModelData['SD2_DataDespacho'])->format('Y-m-d H:i:s');
            }

            if ($this->tableName === 'SE1' && Carbon::parse($erpFlexModelData['SE1_Baixa'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SE1_Baixa'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SE1' && Carbon::parse($erpFlexModelData['SE1_Vencto'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SE1_Vencto'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SE1' && Carbon::parse($erpFlexModelData['SE1_Agendamento'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SE1_Agendamento'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SE2' && Carbon::parse($erpFlexModelData['SE2_Baixa'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SE2_Baixa'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SE2' && Carbon::parse($erpFlexModelData['SE2_Credito'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SE2_Credito'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SE2' && Carbon::parse($erpFlexModelData['SE2_Vencto'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SE2_Vencto'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SE3' && Carbon::parse($erpFlexModelData['SE3_Emissao'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SE3_Emissao'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SE5' && Carbon::parse($erpFlexModelData['SE5_DtBaixa'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SE5_DtBaixa'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SE5' && Carbon::parse($erpFlexModelData['SE5_DtCredito'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SE5_DtCredito'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SE7' && Carbon::parse($erpFlexModelData['SE7_DtEmissao'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SE7_DtEmissao'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SF1' && Carbon::parse($erpFlexModelData['SF1_Emissao'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SF1_Emissao'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SF1' && Carbon::parse($erpFlexModelData['SF1_EmissaoOriginal'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SF1_EmissaoOriginal'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SF1' && Carbon::parse($erpFlexModelData['SF1_DtEntSai'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SF1_DtEntSai'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SF1' && Carbon::parse($erpFlexModelData['SF1_DateRequested'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SF1_DateRequested'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SF1' && Carbon::parse($erpFlexModelData['SF1_ProformInvoice'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SF1_ProformInvoice'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SF2' && Carbon::parse($erpFlexModelData['SF2_Emissao'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SF2_Emissao'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SF2' && Carbon::parse($erpFlexModelData['SF2_EmissaoOriginal'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SF2_EmissaoOriginal'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SF2' && Carbon::parse($erpFlexModelData['SF2_DtEntSai'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SF2_DtEntSai'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SF2' && Carbon::parse($erpFlexModelData['SF2_DtEntrega'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SF2_DtEntrega'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SF2' && Carbon::parse($erpFlexModelData['SF2_DtRecebimento'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SF2_DtRecebimento'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SF3' && Carbon::parse($erpFlexModelData['SF3_DtPrestServ'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SF3_DtPrestServ'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SF3' && Carbon::parse($erpFlexModelData['SF3_DtBloqueioTransmissao'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SF3_DtBloqueioTransmissao'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SF3' && Carbon::parse($erpFlexModelData['SF3_DT_Inicio_PS'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SF3_DT_Inicio_PS'] = '1970-01-01 00:00:00';
            }

            if ($this->tableName === 'SF3' && Carbon::parse($erpFlexModelData['SF3_DT_Termino_PS'])->lt(now()->startOfMillennium())) {
                $erpFlexModelData['SF3_DT_Termino_PS'] = '1970-01-01 00:00:00';
            }

            $class::updateOrCreate([
                $this->tableName . '_ID' => $erpFlexModel->{$this->tableName . '_ID'},
            ], $erpFlexModelData);
        });
    }

    protected function recreateTable(): void
    {
        DB::statement("drop table if exists $this->tableName");

        $schema = DB::connection('erpflex_mysql')->getDoctrineSchemaManager();
        $columns = $schema->listTableColumns($this->tableName);
        $indexes = $schema->listTableIndexes($this->tableName);

        $platform = $schema->getDatabasePlatform();
        $sql = $platform->getListTableColumnsSQL()

        $columnDefinitions = [];
        $primaryKeys = [];

        foreach ($columns as $columnName => $columnData) {
            $columnType = match ($columnData->getType()->getName()) {
                'integer', 'bigint', 'smallint' => 'int',
                'date' => 'date',
                'datetime', 'timestamp' => 'datetime',
                'string' => 'varchar(' . ($columnData->getLength() ?: 255) . ')',
                'text' => 'text',
                'longtext' => 'longtext',
                'float', 'double', 'decimal' => 'double',
                'boolean' => 'tinyint(1)',
                'json' => 'json',
                'binary', 'blob' => 'blob',
                'time' => 'time',
                'year' => 'year',
                default => 'varchar(255)'
            };

            $columnDefinition = "`$columnName` $columnType";

            // Handle nullability
            if (!$columnData->getNotnull()) {
                $columnDefinition .= ' NULL';
            } else {
                $columnDefinition .= ' NOT NULL';
            }

            // Handle default values
            if ($columnData->getDefault() !== null) {
                $default = $columnData->getDefault();

                if (is_string($default)) {
                    $default = "'$default'";
                }

                $columnDefinition .= " DEFAULT $default";
            }

            // Handle auto-increment
            if ($columnData->getAutoincrement()) {
                $columnDefinition .= ' AUTO_INCREMENT';
                $primaryKeys[] = $columnName;
            }

            $columnDefinitions[] = $columnDefinition;
        }

        // Add primary key constraint if exists
        if (!empty($primaryKeys)) {
            $columnDefinitions[] = "PRIMARY KEY (" . implode(', ', array_map(fn($key) => "`$key`", $primaryKeys)) . ")";
        }

        // Add other indexes
        foreach ($indexes as $indexName => $index) {
            if ($indexName === 'PRIMARY' || $indexName === 'primary') {
                continue; // Already handled above
            }

            $indexColumns = array_map(fn($column) => "`$column`", $index->getColumns());
            $indexType = $index->isUnique() ? 'UNIQUE' : 'INDEX';
            $columnDefinitions[] = "$indexType `$indexName` (" . implode(', ', $indexColumns) . ")";
        }

        $createCommand = "CREATE TABLE `$this->tableName` (\n    " . implode(",\n    ", $columnDefinitions) . "\n);";

        DB::statement($createCommand);
    }
}
