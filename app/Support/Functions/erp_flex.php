<?php

use Illuminate\Support\Facades\DB;

function get_erp_flex_api_credentials(): array
{
    $tenantErpFlexApiCredentials = tenant()->erp_flex['api'];

    return [
        'username' => base_decrypt($tenantErpFlexApiCredentials['username']),
        'password' => base_decrypt($tenantErpFlexApiCredentials['password']),
    ];
}

function is_erp_flex_multi_database(): bool
{
    return tenant()->erp_flex['multi_database'];
}

function get_erp_flex_connection_mode(): string
{
    return tenant()->erp_flex['connection_mode'] === 'production'
        ? 'erpflex_mysql'
        : 'qc_erpflex_mysql';
}

function get_erp_flex_shared_tenant_ids(string $table, int $erpFlexTenantId): array
{
    $results = DB::connection(get_erp_flex_connection_mode())
        ->select("select EmpresasCompartilhadas(:tabela, :ea1Id)", [
            'tabela' => $table,
            'ea1Id' => $erpFlexTenantId
        ]);

    return explode(',', $results[0]->{"EmpresasCompartilhadas(?, ?)"});
}
