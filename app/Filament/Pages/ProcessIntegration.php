<?php

namespace App\Filament\Pages;

use App\Console\Commands\RunIntegration;
use Filament\Forms\Components\Select;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Support\Facades\Artisan;

class ProcessIntegration extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $navigationLabel = 'Integrações';
    protected static ?string $title = 'Integrações';
    protected static string $view = 'filament.pages.process-integration';

    public ?string $table;
    public ?string $action;

    protected function getFormSchema(): array
    {
        return [
            Select::make('table')
                ->label('Tabela')
                ->required()
                ->options([
                    'apr_assinaturas' => 'apr_assinaturas',
                    'apr_processos' => 'apr_processos',
                    'CFO' => 'CFO',
                    'CRM_EMP' => 'CRM_EMP',
                    'CRM_OP' => 'CRM_OP',
                    'CRM_PR' => 'CRM_PR',
                    'EA3' => 'EA3',
                    'IE1' => 'IE1',
                    'IE2' => 'IE2',
                    'MVA' => 'MVA',
                    'NCM' => 'NCM',
                    'SA1_SA3' => 'SA1_SA3',
                    'SA1' => 'SA1',
                    'SA3' => 'SA3',
                    'SA4' => 'SA4',
                    'SA6' => 'SA6',
                    'SAE' => 'SAE',
                    'SB1_UM' => 'SB1_UM',
                    'SB1_FATOR' => 'SB1_FATOR',
                    'SB1' => 'SB1',
                    'SB2_SBW' => 'SB2_SBW',
                    'SB2' => 'SB2',
                    'SBA' => 'SBA',
                    'SBL' => 'SBL',
                    'SBW' => 'SBW',
                    'SC2' => 'SC2',
                    'SC3' => 'SC3',
                    'SC4' => 'SC4',
                    'SC5' => 'SC5',
                    'SC6' => 'SC6',
                    'SC7' => 'SC7',
                    'SCA' => 'SCA',
                    'SD1' => 'SD1',
                    'SD2' => 'SD2',
                    'SD3' => 'SD3',
                    'SD4' => 'SD4',
                    'SE1' => 'SE1',
                    'SE2' => 'SE2',
                    'SE3' => 'SE3',
                    'SE4' => 'SE4',
                    'SE5' => 'SE5',
                    'SE5_REF' => 'SE5_REF',
                    'SE7' => 'SE7',
                    'SE8' => 'SE8',
                    'SED' => 'SED',
                    'SEP' => 'SEP',
                    'SF1' => 'SF1',
                    'SF2' => 'SF2',
                    'SF3' => 'SF3',
                    'SG0' => 'SG0',
                    'SG1' => 'SG1',
                    'SPD' => 'SPD',
                    'spx38940001' => 'spx38940001',
                    'spx38940005' => 'spx38940005',
                    'spx38940008' => 'spx38940008',
                    'spx38940012' => 'spx38940012',
                    'spx38940016' => 'spx38940016',
                    'spx38940017' => 'spx38940017',
                    'spx38940018' => 'spx38940018',
                    'spx38940019' => 'spx38940019',
                    'spx38940027' => 'spx38940027',
                    'spx38940031' => 'spx38940031',
                    'spx38940033' => 'spx38940033',
                    'spx38940035' => 'spx38940035',
                    'spx38940036' => 'spx38940036',
                    'spx38940039' => 'spx38940039',
                    'spx38940040' => 'spx38940040',
                    'spx38940043' => 'spx38940043',
                ]),
            Select::make('action')
                ->label('Ação')
                ->options([
                    'process_next_batch' => 'Processar próximo lote',
                    'reprocess_table' => 'Reprocessar tabela',
                    'recreate_table' => 'Recriar tabela',
                ]),
        ];
    }

    public function process()
    {
        Artisan::call(RunIntegration::class, [
            'tenant' => tenant('id'),
            'force' => $this->action !== 'process_next_batch',
            'table_name' => $this->table,
            'truncate' => $this->action === 'recreate_table',
        ]);

        Notification::make()
            ->title('Sucesso!')
            ->body('A integração está rodando em segundo plano.')
            ->send();
    }
}
