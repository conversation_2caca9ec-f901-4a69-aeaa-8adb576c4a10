<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Integration cutoff model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  \Carbon\Carbon $last_apr_assinaturas_update
 * @property  \Carbon\Carbon $last_apr_processos_update
 * @property  \Carbon\Carbon $last_CFO_update
 * @property  \Carbon\Carbon $last_CRM_EMP_update
 * @property  \Carbon\Carbon $last_CRM_OP_update
 * @property  \Carbon\Carbon $last_CRM_PR_update
 * @property  \Carbon\Carbon $last_EA1_update
 * @property  \Carbon\Carbon $last_EA2_update
 * @property  \Carbon\Carbon $last_EA3_update
 * @property  \Carbon\Carbon $last_IE1_update
 * @property  \Carbon\Carbon $last_IE2_update
 * @property  \Carbon\Carbon $last_MVA_update
 * @property  \Carbon\Carbon $last_NCM_update
 * @property  \Carbon\Carbon $last_SA1_SA3_update
 * @property  \Carbon\Carbon $last_SA1_update
 * @property  \Carbon\Carbon $last_SA3_update
 * @property  \Carbon\Carbon $last_SA4_update
 * @property  \Carbon\Carbon $last_SA6_update
 * @property  \Carbon\Carbon $last_SAE_update
 * @property  \Carbon\Carbon $last_SB1_update
 * @property  \Carbon\Carbon $last_SB1_FATOR_update
 * @property  \Carbon\Carbon $last_SB2_SBW_update
 * @property  \Carbon\Carbon $last_SB2_update
 * @property  \Carbon\Carbon $last_SBA_update
 * @property  \Carbon\Carbon $last_SBL_update
 * @property  \Carbon\Carbon $last_SBW_update
 * @property  \Carbon\Carbon $last_SC2_update
 * @property  \Carbon\Carbon $last_SC3_update
 * @property  \Carbon\Carbon $last_SC4_update
 * @property  \Carbon\Carbon $last_SC5_update
 * @property  \Carbon\Carbon $last_SC6_update
 * @property  \Carbon\Carbon $last_SC7_update
 * @property  \Carbon\Carbon $last_SCA_update
 * @property  \Carbon\Carbon $last_SD1_update
 * @property  \Carbon\Carbon $last_SD2_update
 * @property  \Carbon\Carbon $last_SD3_update
 * @property  \Carbon\Carbon $last_SD4_update
 * @property  \Carbon\Carbon $last_SED_update
 * @property  \Carbon\Carbon $last_SEP_update
 * @property  \Carbon\Carbon $last_SE1_update
 * @property  \Carbon\Carbon $last_SE2_update
 * @property  \Carbon\Carbon $last_SE3_update
 * @property  \Carbon\Carbon $last_SE7_update
 * @property  \Carbon\Carbon $last_SF1_update
 * @property  \Carbon\Carbon $last_SF2_update
 * @property  \Carbon\Carbon $last_SF3_update
 * @property  \Carbon\Carbon $last_SG0_update
 * @property  \Carbon\Carbon $last_SG1_update
 * @property  \Carbon\Carbon $last_spx38940012_update
 * @property  \Carbon\Carbon $last_spx38940016_update
 * @property  \Carbon\Carbon $last_spx38940018_update
 * @property  \Carbon\Carbon $last_spx38940019_update
 * @property  \Carbon\Carbon $last_spx38940036_update
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 */
class IntegrationCutoff extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'last_apr_assinaturas_update',
        'last_apr_processos_update',
        'last_CFO_update',
        'last_CRM_EMP_update',
        'last_CRM_OP_update',
        'last_CRM_PR_update',
        'last_EA1_update',
        'last_EA2_update',
        'last_EA3_update',
        'last_IE1_update',
        'last_IE2_update',
        'last_MVA_update',
        'last_NCM_update',
        'last_SA1_SA3_update',
        'last_SA1_update',
        'last_SA3_update',
        'last_SA4_update',
        'last_SA6_update',
        'last_SAE_update',
        'last_SB1_update',
        'last_SB1_FATOR_update',
        'last_SB2_SBW_update',
        'last_SB2_update',
        'last_SBA_update',
        'last_SBL_update',
        'last_SBW_update',
        'last_SC2_update',
        'last_SC3_update',
        'last_SC4_update',
        'last_SC5_update',
        'last_SC6_update',
        'last_SC7_update',
        'last_SCA_update',
        'last_SD1_update',
        'last_SD2_update',
        'last_SD3_update',
        'last_SD4_update',
        'last_SED_update',
        'last_SEP_update',
        'last_SE1_update',
        'last_SE2_update',
        'last_SE3_update',
        'last_SE7_update',
        'last_SF1_update',
        'last_SF2_update',
        'last_SF3_update',
        'last_SG0_update',
        'last_SG1_update',
        'last_spx38940012_update',
        'last_spx38940016_update',
        'last_spx38940018_update',
        'last_spx38940019_update',
        'last_spx38940036_update',
    ];
}
