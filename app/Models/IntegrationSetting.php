<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Integration setting model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  bool $apr_assinaturas
 * @property  bool $apr_processos
 * @property  bool $CFO
 * @property  bool $CRM_EMP
 * @property  bool $CRM_OP
 * @property  bool $CRM_PR
 * @property  bool $EA1
 * @property  bool $EA2
 * @property  bool $EA3
 * @property  bool $IE1
 * @property  bool $IE2
 * @property  bool $MVA
 * @property  bool $NCM
 * @property  bool $SA1_SA3
 * @property  bool $SA1
 * @property  bool $SA3
 * @property  bool $SA4
 * @property  bool $SA6
 * @property  bool $SAE
 * @property  bool $SB1
 * @property  bool $SB1_FATOR
 * @property  bool $SB2_SBW
 * @property  bool $SB2
 * @property  bool $SBA
 * @property  bool $SBL
 * @property  bool $SBW
 * @property  bool $SC2
 * @property  bool $SC3
 * @property  bool $SC4
 * @property  bool $SC5
 * @property  bool $SC6
 * @property  bool $SC7
 * @property  bool $SCA
 * @property  bool $SD1
 * @property  bool $SD2
 * @property  bool $SD3
 * @property  bool $SD4
 * @property  bool $SED
 * @property  bool $SEP
 * @property  bool $SE1
 * @property  bool $SE2
 * @property  bool $SE3
 * @property  bool $SE7
 * @property  bool $SF1
 * @property  bool $SF2
 * @property  bool $SF3
 * @property  bool $SG0
 * @property  bool $SG1
 * @property  bool $spx38940012
 * @property  bool $spx38940016
 * @property  bool $spx38940018
 * @property  bool $spx38940019
 * @property  bool $spx38940036
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 */
class IntegrationSetting extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'apr_assinaturas',
        'apr_processos',
        'CFO',
        'CRM_EMP',
        'CRM_OP',
        'CRM_PR',
        'EA1',
        'EA2',
        'EA3',
        'IE1',
        'IE2',
        'MVA',
        'NCM',
        'SA1_SA3',
        'SA1',
        'SA3',
        'SA4',
        'SA6',
        'SAE',
        'SB1',
        'SB1_FATOR',
        'SB2_SBW',
        'SB2',
        'SBA',
        'SBL',
        'SBW',
        'SC2',
        'SC3',
        'SC4',
        'SC5',
        'SC6',
        'SC7',
        'SCA',
        'SD1',
        'SD2',
        'SD3',
        'SD4',
        'SED',
        'SEP',
        'SE1',
        'SE2',
        'SE3',
        'SE7',
        'SF1',
        'SF2',
        'SF3',
        'SG0',
        'SG1',
        'spx38940012',
        'spx38940016',
        'spx38940018',
        'spx38940019',
        'spx38940036',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'apr_assinaturas' => 'bool',
        'apr_processos' => 'bool',
        'CFO' => 'bool',
        'CRM_EMP' => 'bool',
        'CRM_OP' => 'bool',
        'CRM_PR' => 'bool',
        'EA1' => 'bool',
        'EA2' => 'bool',
        'EA3' => 'bool',
        'MVA' => 'bool',
        'NCM' => 'bool',
        'SA1_SA3' => 'bool',
        'SA1' => 'bool',
        'SA3' => 'bool',
        'SA4' => 'bool',
        'SA6' => 'bool',
        'SAE' => 'bool',
        'SB1' => 'bool',
        'SB2_SBW' => 'bool',
        'SB2' => 'bool',
        'SBA' => 'bool',
        'SBL' => 'bool',
        'SBW' => 'bool',
        'SC2' => 'bool',
        'SC3' => 'bool',
        'SC4' => 'bool',
        'SC5' => 'bool',
        'SC6' => 'bool',
        'SC7' => 'bool',
        'SCA' => 'bool',
        'SD1' => 'bool',
        'SD2' => 'bool',
        'SD3' => 'bool',
        'SD4' => 'bool',
        'SED' => 'bool',
        'SEP' => 'bool',
        'SE1' => 'bool',
        'SE2' => 'bool',
        'SE3' => 'bool',
        'SF1' => 'bool',
        'SF2' => 'bool',
        'SF3' => 'bool',
        'SG0' => 'bool',
        'SG1' => 'bool',
        'spx38940012' => 'bool',
        'spx38940016' => 'bool',
        'spx38940018' => 'bool',
        'spx38940019' => 'bool',
        'spx38940036' => 'bool',
    ];
}
