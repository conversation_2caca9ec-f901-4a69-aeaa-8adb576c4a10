<?php

namespace App\Models\ErpFlex\Scopes;

use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

class HasAprCompanyScope implements Scope
{
    /**
     * Apply the scope to a given Eloquent query builder.
     */
    public function apply(Builder $builder, Model $model): void
    {
        if (empty(tenant())) {
            throw new Exception('Must have tenant.');
        }

        $ids = is_erp_flex_multi_database()
            ? get_erp_flex_shared_tenant_ids($model->getPrefix(), tenant()->erp_flex['company_id'])
            : [tenant()->erp_flex['company_id']];

        $builder->whereIn("id_ea1", $ids);
    }
}
