<?php

namespace App\Models\ErpFlex;

class EA2 extends BaseModel
{
    const CREATED_AT = 'EA2_DT_INC';
    const UPDATED_AT = 'EA2_DT_ALT';

    protected $table = 'EA2';
    protected $primaryKey = 'EA2_ID';
    protected $prefix = 'EA2';

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted()
    {
        self::$useGlobalScope = false;

        parent::booted();
    }
}
