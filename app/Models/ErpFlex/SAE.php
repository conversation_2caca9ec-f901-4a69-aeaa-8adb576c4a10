<?php

namespace App\Models\ErpFlex;

class SAE extends BaseModel
{
    const CREATED_AT = 'SAE_DT_INC';
    const UPDATED_AT = 'SAE_DT_ALT';

    protected $table = 'SAE';
    protected $primaryKey = 'SAE_ID';
    protected $prefix = 'SAE';

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted()
    {
        self::$useGlobalScope = false;
    }
}
