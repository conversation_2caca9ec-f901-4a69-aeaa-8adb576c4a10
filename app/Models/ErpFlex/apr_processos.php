<?php

namespace App\Models\ErpFlex;

use App\Models\ErpFlex\Scopes\HasAprCompanyScope;

class apr_processos extends BaseModel
{
    protected $table = 'apr_processos';
    protected $primaryKey = 'id';
    public $timestamps = false;

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted()
    {
        static::addGlobalScope(new HasAprCompanyScope);
    }
}
