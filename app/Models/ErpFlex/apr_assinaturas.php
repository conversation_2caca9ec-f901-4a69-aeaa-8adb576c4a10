<?php

namespace App\Models\ErpFlex;

use App\Models\ErpFlex\Scopes\HasAprCompanyScope;

class apr_assinaturas extends BaseModel
{
    protected $table = 'apr_assinaturas';
    protected $primaryKey = 'id';
    public $timestamps = false;

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted()
    {
        static::addGlobalScope(new HasAprCompanyScope);
    }
}
