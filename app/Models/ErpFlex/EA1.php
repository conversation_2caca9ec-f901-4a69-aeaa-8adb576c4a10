<?php

namespace App\Models\ErpFlex;

class EA1 extends BaseModel
{
    const CREATED_AT = 'EA1_DT_INC';
    const UPDATED_AT = 'EA1_DT_ALT';

    protected $table = 'EA1';
    protected $primaryKey = 'EA1_ID';
    protected $prefix = 'EA1';

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted()
    {
        self::$useGlobalScope = false;

        parent::booted();
    }
}
