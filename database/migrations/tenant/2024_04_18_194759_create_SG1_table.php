<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
        CREATE TABLE `SG1` (
            `SG1_ID` int NOT NULL AUTO_INCREMENT,
            `SG1_IDEA1` int NOT NULL COMMENT 'Id da Empresa',
            `SG1_IDSG0` int DEFAULT NULL COMMENT 'Id do cabecalho da estrutura',
            `SG1_IDSB1` int NOT NULL DEFAULT '0' COMMENT ' ',
            `SG1_IDSB2` int DEFAULT '0',
            `SG1_Comp` int NOT NULL DEFAULT '0' COMMENT ' ',
            `XXX_IDSB1Old` int DEFAULT NULL,
            `SG1_IDSB2Comp` int DEFAULT '0',
            `SG1_Quant` decimal(11,3) NOT NULL COMMENT ' ',
            `SG1_Fixo` varchar(1) COLLATE latin1_general_ci DEFAULT 'V' COMMENT 'F-Qt. Fixa, V-Qt. Variavel',
            `SG1_Ordem` varchar(35) COLLATE latin1_general_ci DEFAULT NULL,
            `SG1_CRIADOR` int unsigned NOT NULL COMMENT 'Id do Criador',
            `SG1_ALTERADOR` int unsigned NOT NULL DEFAULT '0' COMMENT 'Id do último alterador',
            `SG1_DT_INC` datetime NOT NULL COMMENT 'Data de inclusão do registro',
            `SG1_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteração do registro',
            `SG1_STATUS` int NOT NULL DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
            `SG1_Observacoes` varchar(250) COLLATE latin1_general_ci DEFAULT NULL,
            `SG1_Ativo` int DEFAULT '1' COMMENT '1=Ativo, 0=Inativo',
            `SG1_Fornecimento` int DEFAULT NULL,
            PRIMARY KEY (`SG1_ID`),
            KEY `SG1_IDSB1` (`SG1_IDSB1`),
            KEY `SG1_SB2` (`SG1_IDSB2`),
            KEY `SG1_EA1` (`SG1_IDEA1`),
            KEY `FK_SG1_Comp` (`SG1_Comp`),
            KEY `FK_SG1_SB2Comp` (`SG1_IDSB2Comp`),
            KEY `FK_SG1_IDSG0` (`SG1_IDSG0`),
            KEY `IDEA1_IDSB1_IDSB2` (`SG1_IDEA1`,`SG1_IDSB1`,`SG1_IDSB2`)
          )
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('SG1');
    }
};
