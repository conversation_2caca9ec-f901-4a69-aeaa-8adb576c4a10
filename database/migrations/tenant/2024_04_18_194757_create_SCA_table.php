<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
        CREATE TABLE `SCA` (
            `SCA_ID` int NOT NULL AUTO_INCREMENT,
            `SCA_IDEA1` int NOT NULL COMMENT 'Id da Empresa',
            `SCA_IDSC3` int NOT NULL DEFAULT '0',
            `SCA_IDSEP` int DEFAULT NULL COMMENT 'ID da Forma de Pagamento',
            `SCA_IDSA6` int DEFAULT NULL COMMENT 'Id da Conta Bancária',
            `SCA_Sequencial` int NOT NULL DEFAULT '1' COMMENT 'Número sequencial das parcelas',
            `SCA_Hist` varchar(255) CHARACTER SET latin1 COLLATE latin1_bin NOT NULL COMMENT 'Histórico',
            `SCA_Vencto` datetime NOT NULL COMMENT 'Data de vencimento da parcela ',
            `SCA_Tipo` varchar(13) CHARACTER SET latin1 COLLATE latin1_bin NOT NULL COMMENT 'Tipo do pagamento/recebimento',
            `SCA_tPag` varchar(2) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Meio de Pagamento: 01=Dinheiro, 02=Cheque, 03=Cartão de Crédito, 04=Cartão de Débito, 05=Crédito Loja, 10=Vale Alimentação, 11=Vale Refeição, 12=Vale Presente, 13=Vale Combustível, 15=Boleto Bancário, 16=Depósito Bancário, 17=Pagamento Instantâneo (PIX), 18=Transferência bancária, Carteira Digital, 19=Programa de fidelidade, Cashback, Crédito Virtual, 90= Sem pagamento, 99=Outros',
            `SCA_ComJuros` varchar(1) CHARACTER SET latin1 COLLATE latin1_bin NOT NULL DEFAULT 'N' COMMENT 'Se a parcela tem juros embutido',
            `SCA_Valor` decimal(11,2) DEFAULT NULL COMMENT 'Valor',
            `SCA_CRIADOR` int unsigned NOT NULL COMMENT 'Id do Criador',
            `SCA_ALTERADOR` int unsigned NOT NULL DEFAULT '0' COMMENT 'Id do último alterador',
            `SCA_DT_INC` datetime NOT NULL COMMENT 'Data de inclusão do registro',
            `SCA_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteração do registro',
            `SCA_STATUS` int NOT NULL DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
            `SCA_TaxaJuros` decimal(9,6) DEFAULT '0.000000',
            `SCA_ValorJuros` decimal(11,2) DEFAULT '0.00',
            PRIMARY KEY (`SCA_ID`),
            KEY `FK_SCA_EA1` (`SCA_IDEA1`),
            KEY `FK_SCA_SC3` (`SCA_IDSC3`),
            KEY `FK_SCA_SEP` (`SCA_IDSEP`),
            KEY `FK_SCA_SA6` (`SCA_IDSA6`)
          )
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('SCA');
    }
};
