<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
        CREATE TABLE `SEP` (
            `SEP_ID` int NOT NULL AUTO_INCREMENT,
            `SEP_IDEA1` int NOT NULL COMMENT 'Id da Empresa',
            `SEP_IDSA6` int DEFAULT NULL COMMENT 'ID do Banco, no caso de ''A Vista''',
            `SEP_IDSA8` int DEFAULT NULL COMMENT 'ID do Cartao de Credito',
            `SEP_Desc` varchar(60) COLLATE latin1_general_ci NOT NULL COMMENT 'DescriÃ§Ã£o',
            `SEP_Tipo` varchar(13) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Tipo de Pagto: $,PRZ,PRE,CCn, MPG',
            `SEP_tPag` varchar(2) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Meio de <PERSON>nto: 01=Dinheiro, 02=Cheque, 03=Cartão de Crédito, 04=Cartão de Débito, 05=Crédito Loja, 10=Vale Alimentação, 11=Vale Refeição, 12=Vale Presente, 13=Vale Combustível, 15=Boleto Bancário, 16=Depósito Bancário, 17=Pagamento Instantâneo (PIX), 18=Transferência bancária, Carteira Digital, 19=Programa de fidelidade, Cashback, Crédito Virtual, 90= Sem pagamento, 99=Outros',
            `SEP_AutomComercial` varchar(1) COLLATE latin1_general_ci DEFAULT 'N',
            `SEP_Parcelas` int DEFAULT '1' COMMENT 'Nr. de parcelas',
            `SEP_Intervalo` int DEFAULT '0' COMMENT 'Intervalo em dias entre as parcelas',
            `SEP_IntervaloVariavel` varchar(60) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Untervalos variaveis entre as parcelas (ex.: 15,40,20)',
            `SEP_Vencto` int DEFAULT '0' COMMENT 'Dia do vencimento da parcela',
            `SEP_ValMinimoParc` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT 'Valor mÃ­nimo da parcela',
            `SEP_JurosFinDia` decimal(8,6) NOT NULL DEFAULT '0.000000' COMMENT 'Juros Financeiro ao Dia para paamentos parcelados',
            `SEP_Banco` varchar(3) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Numero do Banco',
            `SEP_Agencia` varchar(4) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Numero da Agencia',
            `SEP_Conta` varchar(50) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Numero da Conta Bancaria',
            `SEP_ContaDig` varchar(3) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Digito da Conta Bancaria',
            `SEP_CRIADOR` int unsigned DEFAULT NULL COMMENT 'Id do Criador',
            `SEP_ALTERADOR` int unsigned DEFAULT '0' COMMENT 'Id do Ãºltimo alterador',
            `SEP_DT_INC` datetime DEFAULT NULL COMMENT 'Data de inclusÃ£o do registro',
            `SEP_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteraÃ§Ã£o do registro',
            `SEP_STATUS` int DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
            PRIMARY KEY (`SEP_ID`),
            KEY `SEP_IDEA1` (`SEP_IDEA1`,`SEP_Desc`),
            KEY `FK_SEP_SA6` (`SEP_IDSA6`),
            KEY `IDSA8` (`SEP_IDSA8`)
          )
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('SEP');
    }
};
