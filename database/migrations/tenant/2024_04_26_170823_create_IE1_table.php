<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
        CREATE TABLE `IE1` (
          `IE1_ID` int NOT NULL AUTO_INCREMENT,
          `IE1_IDEA1` int NOT NULL COMMENT 'Id da Empresa',
          `IE1_IDSPD` int DEFAULT '0' COMMENT 'ID da Conta Sped',
          `IE1_IDBL1` int DEFAULT '0' COMMENT 'ID da Linha do Balanco',
          `IE1_Tipo` varchar(1) COLLATE latin1_general_ci NOT NULL COMMENT 'Tipo',
          `IE1_Doc` varchar(20) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Documento',
          `IE1_Desc` varchar(30) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Descrição',
          `IE1_SaldoAnt` decimal(17,2) NOT NULL COMMENT 'Saldo anterior',
          `IE1_SaldoAtu` decimal(17,2) NOT NULL COMMENT 'Saldo atual',
          `IE1_CRIADOR` int unsigned NOT NULL COMMENT 'Id do Criador',
          `IE1_ALTERADOR` int unsigned NOT NULL DEFAULT '0' COMMENT 'Id do último alterador',
          `IE1_DT_INC` datetime NOT NULL COMMENT 'Data de inclusão do registro',
          `IE1_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteração do registro',
          `IE1_STATUS` int NOT NULL DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
          PRIMARY KEY (`IE1_ID`),
          KEY `IE1_Desc` (`IE1_IDEA1`,`IE1_Desc`)
        )
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('IE1');
    }
};
