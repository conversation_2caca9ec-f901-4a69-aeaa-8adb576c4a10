<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
            CREATE TABLE `SE5_REF` (
                `SE5_REF_ID` int NOT NULL AUTO_INCREMENT,
                `SE5_REF_IDEA1` int NOT NULL COMMENT 'Id da Empresa',
                `SE5_REF_IDSE5` int DEFAULT NULL,
                `SE5_REF_IDSE5_Ref` int DEFAULT NULL,
                `SE5_REF_CRIADOR` int unsigned DEFAULT NULL COMMENT 'Id do Criador',
                `SE5_REF_ALTERADOR` int unsigned DEFAULT '0' COMMENT 'Id do último alterador',
                `SE5_REF_DT_INC` datetime DEFAULT NULL COMMENT 'Data de inclusão do registro',
                `SE5_REF_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteração do registro',
                `SE5_REF_STATUS` int DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
                PRIMARY KEY (`SE5_REF_ID`)
            )
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('spx38940035');
    }
};
