<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
        CREATE TABLE `NCM` (
          `NCM_ID` int NOT NULL AUTO_INCREMENT,
          `NCM_IDEA1` int DEFAULT NULL,
          `NCM_Codigo` varchar(8) DEFAULT NULL COMMENT 'Codigo NCM',
          `NCM_EX` varchar(3) DEFAULT '000' COMMENT 'EX',
          `NCM_Genero` varchar(2) DEFAULT '' COMMENT 'Genero do Produto ou Servico',
          `NCM_Desc` varchar(100) DEFAULT NULL COMMENT 'Descricao',
          `NCM_FAT_UM` varchar(6) DEFAULT '' COMMENT 'Unidade de Medida',
          `NCM_FAT_PII` decimal(5,2) DEFAULT '0.00' COMMENT '% Imp. Importacao',
          `NCM_FAT_PIPI` decimal(5,2) DEFAULT '0.00' COMMENT '% IPI',
          `NCM_FAT_ClsEnquadrIPI` varchar(5) DEFAULT '' COMMENT 'Classe de enquadramento IPI',
          `NCM_FAT_CodEnquadrIPI` varchar(3) DEFAULT '' COMMENT 'Codigo de enquadramento IPI',
          `NCM_FAT_IncluirIPIBaseICMS` varchar(1) DEFAULT '0' COMMENT 'Incluir IPI na BC do ICMS: 0-Nao, 1-Sim',
          `NCM_FAT_IncluirIPIBaseICMS_ST` varchar(1) DEFAULT '0' COMMENT 'Incluir IPI na BC do ICMS_ST: 0-Nao, 1-Sim',
          `NCM_FAT_IncluirDescBaseICMS` varchar(1) DEFAULT '0' COMMENT 'Incluir desconto na BC do ICMS: 0-Nao, 1-Sim',
          `NCM_FAT_IncluirDescBaseICMS_ST` varchar(1) DEFAULT '0' COMMENT 'Incluir desconto na BC do ICMS ST: 0-Nao, 1-Sim',
          `NCM_FAT_PImp` decimal(5,2) DEFAULT '0.00' COMMENT '% ICMS',
          `NCM_FAT_PPIS` decimal(5,2) DEFAULT '0.00' COMMENT '% PIS',
          `NCM_FAT_PCOFINS` decimal(5,2) DEFAULT '0.00' COMMENT '% COFINS',
          `NCM_FAT_PMARGEM` decimal(5,2) DEFAULT '0.00' COMMENT '% Margem de valor adicional ICMS ST',
          `NCM_FAT_PDif` decimal(9,4) DEFAULT '0.0000' COMMENT '% do Diferimento',
          `NCM_FAT_SitTribICMS` varchar(2) DEFAULT '' COMMENT 'Sit. Trib. ICMS',
          `NCM_FAT_SitTribICMSSimples` varchar(3) DEFAULT '' COMMENT 'Sit. Trib. ICMS para empresas no Simples',
          `NCM_FAT_ModBaseICMS` varchar(1) DEFAULT '' COMMENT 'Modalid. determ. BC ICMS',
          `NCM_FAT_PRedBaseICMS` decimal(9,4) DEFAULT '0.0000',
          `NCM_FAT_ModBaseICMS_ST` varchar(1) DEFAULT '' COMMENT 'Modalid. determ. BC ICMS ST',
          `NCM_FAT_PRedBaseICMS_ST` decimal(9,4) DEFAULT '0.0000',
          `NCM_FAT_PAproxNacFed` decimal(5,2) DEFAULT '0.00' COMMENT '% Aproximado da carga tributaria federal de produtos Nacionais',
          `NCM_FAT_PAproxImpFed` decimal(5,2) DEFAULT '0.00' COMMENT '% Aproximado da carga tributaria federal de produtos Importados',
          `NCM_FAT_PAproxEst` decimal(5,2) DEFAULT '0.00' COMMENT '% Aproximado da carga tributaria estadual de produtos',
          `NCM_FAT_PAproxMun` decimal(5,2) DEFAULT '0.00' COMMENT '% Aproximado da carga tributaria municipal de produtos',
          `NCM_FAT_CEST` varchar(7) CHARACTER SET latin1 COLLATE latin1_general_ci DEFAULT '' COMMENT 'Codigo Especificador da Substituicao Tributaria',
          `NCM_COM_UM` varchar(2) DEFAULT '' COMMENT 'Unidade de Medida',
          `NCM_COM_PII` decimal(5,2) DEFAULT '0.00' COMMENT '% Imp. Importacao',
          `NCM_COM_PIPI` decimal(5,2) DEFAULT '0.00' COMMENT '% IPI',
          `NCM_COM_ClsEnquadrIPI` varchar(5) DEFAULT '' COMMENT 'Classe de enquadramento IPI',
          `NCM_COM_CodEnquadrIPI` varchar(3) DEFAULT '' COMMENT 'Codigo de enquadramento IPI',
          `NCM_COM_IncluirIPIBaseICMS` varchar(1) DEFAULT '0' COMMENT 'Incluir IPI na BC do ICMS: 0-Nao, 1-Sim',
          `NCM_COM_IncluirIPIBaseICMS_ST` varchar(1) DEFAULT '0' COMMENT 'Incluir IPI na BC do ICMS_ST: 0-Nao, 1-Sim',
          `NCM_COM_PImp` decimal(5,2) DEFAULT '0.00' COMMENT '% ICMS',
          `NCM_COM_PPIS` decimal(5,2) DEFAULT '0.00' COMMENT '% PIS',
          `NCM_COM_PPIS_CREDITO` decimal(5,2) DEFAULT '0.00' COMMENT 'Porcentual máximo que empresa do lucro real poderia creditar-se',
          `NCM_COM_PCOFINS` decimal(5,2) DEFAULT '0.00' COMMENT '% COFINS',
          `NCM_COM_PCOFINS_CREDITO` decimal(5,2) DEFAULT '0.00' COMMENT 'Porcentual máximo que empresa do lucro real poderia creditar-se',
          `NCM_COM_PMARGEM` decimal(5,2) DEFAULT '0.00' COMMENT '% Margem de valor adicional ICMS ST',
          `NCM_COM_PDif` decimal(9,4) DEFAULT '0.0000' COMMENT '% do Diferimento',
          `NCM_COM_SitTribICMS` varchar(2) DEFAULT '' COMMENT 'Sit. Trib. ICMS',
          `NCM_COM_SitTribICMSSimples` varchar(3) DEFAULT '' COMMENT 'Sit. Trib. ICMS para empresas no Simples',
          `NCM_COM_ModBaseICMS` varchar(1) DEFAULT '' COMMENT 'Modalid. determ. BC ICMS',
          `NCM_COM_PRedBaseICMS` decimal(9,4) DEFAULT '0.0000',
          `NCM_COM_ModBaseICMS_ST` varchar(1) DEFAULT '' COMMENT 'Modalid. determ. BC ICMS ST',
          `NCM_COM_PRedBaseICMS_ST` decimal(9,4) DEFAULT '0.0000',
          `NCM_COM_PAproxNacFed` decimal(5,2) DEFAULT '0.00' COMMENT '% Aproximado da carga tributaria federal de produtos Nacionais',
          `NCM_COM_PAproxImpFed` decimal(5,2) DEFAULT '0.00' COMMENT '% Aproximado da carga tributaria federal de produtos Importados',
          `NCM_COM_PAproxEst` decimal(5,2) DEFAULT '0.00' COMMENT '% Aproximado da carga tributaria estadual de produtos',
          `NCM_COM_PAproxMun` decimal(5,2) DEFAULT '0.00' COMMENT '% Aproximado da carga tributaria municipal de produtos',
          `NCM_COM_CEST` varchar(7) CHARACTER SET latin1 COLLATE latin1_general_ci DEFAULT '' COMMENT 'Codigo Especificador da Substituicao Tributaria',
          `NCM_FAT_SitTribIPI` varchar(2) CHARACTER SET latin1 COLLATE latin1_general_ci DEFAULT NULL COMMENT 'CST IPI para o Faturamento',
          `NCM_FAT_TipoCalculoIPI` varchar(1) DEFAULT 'P' COMMENT 'Tipo de Calculo (P)ercentual (V)alor',
          `NCM_FAT_SitTribPIS` varchar(2) CHARACTER SET latin1 COLLATE latin1_general_ci DEFAULT NULL COMMENT 'CST PIS para o Faturamento',
          `NCM_FAT_TipoCalculoPIS` varchar(1) DEFAULT 'P' COMMENT 'Tipo de Calculo (P)ercentual (V)alor',
          `NCM_FAT_SitTribCOFINS` varchar(2) CHARACTER SET latin1 COLLATE latin1_general_ci DEFAULT NULL COMMENT 'CST COFINS para o Faturamento',
          `NCM_FAT_TipoCalculoCOFINS` varchar(1) DEFAULT 'P' COMMENT 'Tipo de Calculo (P)ercentual (V)alor',
          `NCM_COM_SitTribIPI` varchar(2) DEFAULT NULL COMMENT 'CST IPI para o Compras',
          `NCM_COM_TipoCalculoIPI` varchar(1) DEFAULT 'P' COMMENT 'Tipo de Calculo (P)ercentual (V)alor',
          `NCM_COM_SitTribPIS` varchar(2) DEFAULT NULL COMMENT 'CST PIS para o Compras',
          `NCM_COM_TipoCalculoPIS` varchar(1) DEFAULT 'P' COMMENT 'Tipo de Calculo (P)ercentual (V)alor',
          `NCM_COM_SitTribCOFINS` varchar(2) DEFAULT NULL COMMENT 'CST COFINS para o Compras',
          `NCM_COM_TipoCalculoCOFINS` varchar(1) DEFAULT 'P' COMMENT 'Tipo de Calculo (P)ercentual (V)alor',
          `NCM_PDV_SitTribIPI` varchar(2) DEFAULT NULL COMMENT 'CST IPI Para o PDV',
          `NCM_PDV_TipoCalculoIPI` varchar(1) DEFAULT 'P' COMMENT 'Tipo de Calculo (P)ercentual (V)alor',
          `NCM_PDV_PIPI` decimal(5,2) DEFAULT '0.00' COMMENT '% IPI PDV',
          `NCM_PDV_SitTribPIS` varchar(2) DEFAULT NULL COMMENT 'CST PIS para o PDV',
          `NCM_PDV_TipoCalculoPIS` varchar(1) DEFAULT 'P' COMMENT 'Tipo Calculo (P)ercentual (V)alor',
          `NCM_PDV_PPIS` decimal(5,2) DEFAULT '0.00' COMMENT '% PIS para o PDV',
          `NCM_PDV_SitTribCOFINS` varchar(2) DEFAULT NULL COMMENT 'CST Cofins para o PDV',
          `NCM_PDV_TipoCalculoCOFINS` varchar(1) DEFAULT 'P' COMMENT 'Tipo Calculo (P)ercentual (V)alor',
          `NCM_PDV_PCOFINS` decimal(5,2) DEFAULT '0.00' COMMENT '% COFINS para o PDV',
          `NCM_PDV_Origem` varchar(1) DEFAULT '0' COMMENT 'Origem para o PDV',
          `NCM_PDV_SitTribICMS` varchar(2) DEFAULT NULL COMMENT 'Sit. Trib. ICMS para o PDV',
          `NCM_PDV_SitTribICMSSimples` varchar(3) DEFAULT NULL COMMENT 'Sit. Trib. ICMS para empresas no Simples para o PDV',
          `NCM_PDV_PICMS` decimal(5,2) DEFAULT '0.00' COMMENT 'Aliquota ICMS para o PDV',
          `NCM_EnviadoZanthus` varchar(1) DEFAULT 'N' COMMENT 'Indica se foi enviado para o PDV',
          `NCM_AutomComercial` varchar(1) DEFAULT 'N' COMMENT 'Indica se é utilizado no PDV',
          `NCM_IncluiReg1400` varchar(1) DEFAULT NULL COMMENT 'Indica se produtos com NCM marcado deve ser levado para SpedFiscal Bloco 1400 ',
          `NCM_CodigoIPM` varchar(60) DEFAULT NULL COMMENT 'Código de Item para IPM da UF - indice participação dos municipios',
          `NCM_CRIADOR` int unsigned DEFAULT '0' COMMENT 'Id do Criador',
          `NCM_ALTERADOR` int unsigned DEFAULT '0' COMMENT 'Id do último alterador',
          `NCM_DT_INC` datetime DEFAULT NULL COMMENT 'Data de inclusão do registro',
          `NCM_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteração do registro',
          `NCM_STATUS` int DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
          `NCM_Chave` varchar(50) DEFAULT NULL COMMENT 'Chave unica: Codigo+Empresa (campo provisorio; enquanto tiver Codigo+Empresa repetido)',
          `NCM_vigenciainicio` date DEFAULT NULL COMMENT 'Vigencia Inicio',
          `NCM_vigenciafim` date DEFAULT NULL COMMENT 'Vigencia Fim',
          PRIMARY KEY (`NCM_ID`),
          UNIQUE KEY `IDEA1_Chave` (`NCM_IDEA1`,`NCM_Chave`),
          KEY `Codigo` (`NCM_Codigo`),
          KEY `IDEA1` (`NCM_IDEA1`),
          KEY `Desc` (`NCM_Desc`)
        )
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('NCM');
    }
};
