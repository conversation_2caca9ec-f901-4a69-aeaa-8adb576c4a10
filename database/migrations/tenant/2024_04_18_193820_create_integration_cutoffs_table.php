<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('integration_cutoffs', function (Blueprint $table) {
            $table->id();
            $table->dateTime('last_apr_assinaturas_update')->nullable();
            $table->dateTime('last_apr_processos_update')->nullable();
            $table->dateTime('last_CFO_update')->nullable();
            $table->dateTime('last_CRM_EMP_update')->nullable();
            $table->dateTime('last_CRM_OP_update')->nullable();
            $table->dateTime('last_CRM_PR_update')->nullable();
            $table->dateTime('last_EA1_update')->nullable();
            $table->dateTime('last_EA2_update')->nullable();
            $table->dateTime('last_EA3_update')->nullable();
            $table->dateTime('last_MVA_update')->nullable();
            $table->dateTime('last_NCM_update')->nullable();
            $table->dateTime('last_SA1_SA3_update')->nullable();
            $table->dateTime('last_SA1_update')->nullable();
            $table->dateTime('last_SA3_update')->nullable();
            $table->dateTime('last_SA4_update')->nullable();
            $table->dateTime('last_SA6_update')->nullable();
            $table->dateTime('last_SAE_update')->nullable();
            $table->dateTime('last_SB1_update')->nullable();
            $table->dateTime('last_SB2_SBW_update')->nullable();
            $table->dateTime('last_SB2_update')->nullable();
            $table->dateTime('last_SBA_update')->nullable();
            $table->dateTime('last_SBL_update')->nullable();
            $table->dateTime('last_SBW_update')->nullable();
            $table->dateTime('last_SC2_update')->nullable();
            $table->dateTime('last_SC3_update')->nullable();
            $table->dateTime('last_SC4_update')->nullable();
            $table->dateTime('last_SC5_update')->nullable();
            $table->dateTime('last_SC6_update')->nullable();
            $table->dateTime('last_SC7_update')->nullable();
            $table->dateTime('last_SCA_update')->nullable();
            $table->dateTime('last_SD1_update')->nullable();
            $table->dateTime('last_SD2_update')->nullable();
            $table->dateTime('last_SD3_update')->nullable();
            $table->dateTime('last_SD4_update')->nullable();
            $table->dateTime('last_SED_update')->nullable();
            $table->dateTime('last_SEP_update')->nullable();
            $table->dateTime('last_SE2_update')->nullable();
            $table->dateTime('last_SF1_update')->nullable();
            $table->dateTime('last_SF2_update')->nullable();
            $table->dateTime('last_SF3_update')->nullable();
            $table->dateTime('last_SG0_update')->nullable();
            $table->dateTime('last_SG1_update')->nullable();
            $table->dateTime('last_spx38940012_update')->nullable();
            $table->dateTime('last_spx38940016_update')->nullable();
            $table->dateTime('last_spx38940018_update')->nullable();
            $table->dateTime('last_spx38940019_update')->nullable();
            $table->dateTime('last_spx38940036_update')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('integration_cutoffs');
    }
};
