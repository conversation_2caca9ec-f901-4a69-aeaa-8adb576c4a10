<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
        CREATE TABLE `SC7` (
            `SC7_ID` int NOT NULL AUTO_INCREMENT,
            `SC7_IDEA1` int NOT NULL COMMENT 'Id da Empresa',
            `SC7_IDSC5` int NOT NULL DEFAULT '0',
            `SC7_IDSEP` int DEFAULT NULL COMMENT 'ID da Forma de Pagamento',
            `SC7_Sequencial` int NOT NULL DEFAULT '1' COMMENT 'NÃºmero sequencial das parcelas',
            `SC7_Hist` varchar(255) COLLATE latin1_bin NOT NULL COMMENT 'Histï¿½rico',
            `SC7_Vencto` datetime NOT NULL COMMENT ' ',
            `SC7_Tipo` varchar(13) COLLATE latin1_bin NOT NULL COMMENT ' ',
            `SC7_tPag` varchar(2) COLLATE latin1_bin DEFAULT '' COMMENT 'Meio de Pagamento: 01=Dinheiro, 02=Cheque, 03=Cartão de Crédito, 04=Cartão de Débito, 05=Crédito Loja, 10=Vale Alimentação, 11=Vale Refeição, 12=Vale Presente, 13=Vale Combustível, 15=Boleto Bancário, 16=Depósito Bancário, 17=Pagamento Instantâneo (PIX), 18=Transferência bancária, Carteira Digital, 19=Programa de fidelidade, Cashback, Crédito Virtual, 90= Sem pagamento, 99=Outros',
            `SC7_ComJuros` varchar(1) COLLATE latin1_bin NOT NULL DEFAULT 'N' COMMENT 'Se a parcela tem juros embutido',
            `SC7_Valor` decimal(11,2) DEFAULT NULL COMMENT 'Valor',
            `SC7_Intervalo` int DEFAULT NULL COMMENT 'Intervalo em dias entre as parcelas',
            `SC7_CRIADOR` int unsigned NOT NULL COMMENT 'Id do Criador',
            `SC7_ALTERADOR` int unsigned NOT NULL DEFAULT '0' COMMENT 'Id do Ãºltimo alterador',
            `SC7_DT_INC` datetime NOT NULL COMMENT 'Data de inclusÃ£o do registro',
            `SC7_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteraÃ§Ã£o do registro',
            `SC7_STATUS` int NOT NULL DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
            `SC7_IDSA6` int DEFAULT '0' COMMENT 'Id da conta Bancária',
            `SC7_TaxaJuros` decimal(9,6) DEFAULT '0.000000',
            `SC7_ValorJuros` decimal(11,2) DEFAULT '0.00',
            PRIMARY KEY (`SC7_ID`),
            KEY `FK_SC7_EA1` (`SC7_IDEA1`),
            KEY `FK_SC7_SC5` (`SC7_IDSC5`),
            KEY `FK_SC7_SEP` (`SC7_IDSEP`),
            KEY `FK_SC7_SA6` (`SC7_IDSA6`)
          )
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('SC7');
    }
};
