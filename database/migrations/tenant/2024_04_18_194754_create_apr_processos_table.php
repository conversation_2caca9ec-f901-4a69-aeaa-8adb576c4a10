<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
        CREATE TABLE `apr_processos` (
          `id` int NOT NULL AUTO_INCREMENT,
          `id_ea1` int NOT NULL COMMENT 'Id da empresa',
          `alias` varchar(255) DEFAULT NULL,
          `id_processo` int DEFAULT NULL,
          `valor` decimal(30,5) DEFAULT NULL,
          `historico` varchar(255) DEFAULT NULL,
          `status` int DEFAULT NULL,
          `data` datetime DEFAULT NULL,
          `user_id` int DEFAULT NULL,
          `user_perfil` int DEFAULT NULL,
          `prox_aprovador` varchar(255) DEFAULT NULL,
          `assinatura` int DEFAULT NULL,
          `valores` text COMMENT 'criterios de aprovacao',
          `id_header` int DEFAULT NULL,
          `criterios` longtext,
          PRIMARY KEY (`id`),
          KEY `alias` (`alias`),
          KEY `ID_EA1` (`id_ea1`),
          KEY `idprocesso_alias_status` (`id_processo`,`alias`,`status`)
        );
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('apr_processos');
    }
};
