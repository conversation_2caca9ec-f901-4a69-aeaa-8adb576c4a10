<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
        CREATE TABLE `SF2` (
            `SF2_ID` int NOT NULL AUTO_INCREMENT,
            `SF2_IDEA1` int NOT NULL COMMENT 'Id da Empresa',
            `SF2_IDSF3` int DEFAULT '0',
            `SF2_IDSF1Ref` int DEFAULT '0' COMMENT 'ID da NF Referenciada',
            `SF2_IDSF2Ref` int DEFAULT '0' COMMENT 'ID da NF Referenciada',
            `SF2_IDMN2Nivel` int DEFAULT '0' COMMENT 'ID do Perfil para determinar o Nivel de permissao de acesso aos registros: 9-maximo, 0-minimo',
            `SF2_Chave_Acres_Desc` varchar(14) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Chave de Busca para registros de Acrescimo e Desconto',
            `SF2_IDSF1PIS` int DEFAULT NULL COMMENT 'ID da Guia que apurou o PIS',
            `SF2_IDSF1COFINS` int DEFAULT NULL COMMENT 'ID da Guia que apurou o Confins',
            `SF2_IDSF1CSLL` int DEFAULT NULL COMMENT 'ID da Guia que apurou o CSLL',
            `SF2_IDSF1IRPJ` int DEFAULT NULL COMMENT 'ID da Guia que apurou o IRPJ',
            `SF2_IDSF1ICMS` int DEFAULT NULL COMMENT 'ID da Guia que apurou o ICMS',
            `SF2_IDSF1IPI` int DEFAULT NULL COMMENT 'ID da Guia que apurou o IPI',
            `SF2_IDSF1ISS` int DEFAULT NULL COMMENT 'ID da Guia que apurou o ISS',
            `SF2_IDAS3` int DEFAULT '0' COMMENT 'ID da Assinatura',
            `SF2_IDSD5` int DEFAULT NULL COMMENT 'ID do Contrato',
            `SF2_Emissao` datetime NOT NULL COMMENT ' ',
            `SF2_HrEmissao` time DEFAULT '00:00:00' COMMENT 'Hora da Emissao',
            `SF2_EmissaoOriginal` datetime  COMMENT 'Emissao original, antes da alteracao pela baixa do titulo',
            `SF2_DtEntSai` datetime NOT NULL COMMENT 'Data de Saida',
            `SF2_Doc` varchar(30) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Documento',
            `SF2_NumSeq` int DEFAULT '0' COMMENT 'Numero Sequencial',
            `SF2_NrNFe` int DEFAULT '0' COMMENT 'Numero sequencial da NFe',
            `SF2_IDSED` int NOT NULL COMMENT ' ',
            `SF2_IDSA1` int NOT NULL COMMENT 'Id Clientes/Fornecedores',
            `SF2_IDSA1Relacionado` int DEFAULT '0' COMMENT 'Id do Cliente Relacionado (utilizado para convenios)',
            `SF2_IDSC5` int DEFAULT '0' COMMENT 'ID do Orcamento',
            `SF2_IDSA1Entrega` int DEFAULT '0' COMMENT 'Id do cliente de entrega',
            `SF2_Hist` varchar(255) COLLATE latin1_general_ci NOT NULL COMMENT 'Histï¿½rico',
            `SF2_DifAliquota` varchar(255) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Msg Diferencial de Aliquota',
            `SF2_ValDifAliquota` decimal(18,2) DEFAULT '0.00' COMMENT 'Valor Diferencial de Aliquota',
            `SF2_TabelaPreco` int DEFAULT NULL COMMENT 'Numero da Tabela de Preco',
            `SF2_CodigoFrete` varchar(5) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Código numérico que indica o tipo de frete escolhido na hora da compra',
            `SF2_Transportadora` varchar(40) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Identificação da transportadora',
            `SF2_DtEntrega` date DEFAULT NULL COMMENT 'Data prevista para entrega',
            `SF2_RecebidoPor` varchar(150) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Data prevista para entrega',
            `SF2_DtRecebimento` date DEFAULT NULL COMMENT 'Data do recebimento',
            `SF2_SitDespacho` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Situação do despacho: ''P'' - parcial; ''T'' - Total',
            `SF2_EnviadoProtheus` varchar(1) COLLATE latin1_general_ci DEFAULT 'N' COMMENT 'S-foi enviado ao Protheus, N-ainda nao enviado',
            `SF2_ChaveZanthus` varchar(100) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Chave Unica Zanthus: Data + Loja + PDV + Cupom + Contador de Operacoes + Sequencial de Transacoes no Dia',
            `SF2_NrPDV` int DEFAULT NULL COMMENT 'Numero do PDV',
            `SF2_CPFCNPJ_CONT` varchar(150) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Codigo Especificador da Substituicao Tributaria',
            `SF2_Ativo` varchar(1) COLLATE latin1_general_ci DEFAULT '1' COMMENT '1-Ativo, 0-Inativo',
            `SF2_CRIADOR` int unsigned NOT NULL COMMENT 'Id do Criador',
            `SF2_ALTERADOR` int unsigned NOT NULL DEFAULT '0' COMMENT 'Id do último alterador',
            `SF2_DT_INC` datetime NOT NULL COMMENT 'Data de inclusão do registro',
            `SF2_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteração do registro',
            `SF2_STATUS` int NOT NULL DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
            `SF2_IDMOEDA_COT` int DEFAULT NULL COMMENT 'Chave estrangeira com MOEDA_COT',
            `SF2_IDMOEDA` int DEFAULT NULL COMMENT 'Chave estrangeira com Moeda',
            PRIMARY KEY (`SF2_ID`),
            KEY `SF2_SC5` (`SF2_IDSC5`),
            KEY `SF2_Emissao` (`SF2_IDEA1`,`SF2_Emissao`),
            KEY `FK_SF2_SED` (`SF2_IDSED`),
            KEY `FK_SF2_SA1` (`SF2_IDSA1`),
            KEY `FK_SF2_SD5` (`SF2_IDSD5`),
            KEY `ID_SF2_Chave` (`SF2_IDEA1`,`SF2_Chave_Acres_Desc`),
            KEY `FK_SF2_SA1Entrega` (`SF2_IDSA1Entrega`),
            KEY `IDSF3` (`SF2_IDSF3`),
            KEY `IDEA1_ChaveZanthus` (`SF2_IDEA1`,`SF2_ChaveZanthus`),
            KEY `FK_SF2_SF1` (`SF2_IDSF1Ref`),
            KEY `FK_SF2_SF2` (`SF2_IDSF2Ref`),
            KEY `IDEA1_NrPDV` (`SF2_IDEA1`,`SF2_NrPDV`),
            KEY `IDEA1_Ativo` (`SF2_IDEA1`,`SF2_Ativo`),
            KEY `IDX_IDEA1_NumSeq` (`SF2_IDEA1`,`SF2_NumSeq`),
            KEY `FK_SF2_MOEDA_COT` (`SF2_IDMOEDA_COT`),
            KEY `FK_SF2_MOEDA` (`SF2_IDMOEDA`)
          )
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('SF2');
    }
};
