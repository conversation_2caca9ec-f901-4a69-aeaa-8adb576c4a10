<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
        -- erpflex.SBL definition

CREATE TABLE `SBL` (
  `SBL_ID` int NOT NULL AUTO_INCREMENT,
  `SBL_IDEA1` int DEFAULT '0' COMMENT 'Id da empresa',
  `SBL_Desc` varchar(50) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Numero do Lote',
  `SBL_IDSB1` int DEFAULT '0',
  `SBL_IDSB2` int DEFAULT '0',
  `SBL_IDSD1` int DEFAULT NULL,
  `SBL_IDSD2` int DEFAULT NULL,
  `SBL_DtEntrada` datetime  COMMENT 'Data da Entrada',
  `SBL_DtValidade` datetime  COMMENT 'Data de Validade',
  `SBL_DtFabricacao` datetime  COMMENT 'Data de Fabricacao',
  `SBL_CodAgreg` varchar(20) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Código de Agregação',
  `SBL_QAnt` decimal(10,3) DEFAULT '0.000',
  `SBL_QAtu` decimal(10,3) DEFAULT '0.000',
  `SBL_Reserva` decimal(18,8) DEFAULT '0.00000000' COMMENT 'Qtd reservada no Lote',
  `SBL_CRIADOR` int unsigned NOT NULL DEFAULT '0' COMMENT 'Id do Criador',
  `SBL_ALTERADOR` int unsigned NOT NULL DEFAULT '0' COMMENT 'Id do último alterador',
  `SBL_DT_INC` datetime NOT NULL  COMMENT 'Data de inclusão do registro',
  `SBL_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteração do registro',
  `SBL_STATUS` int NOT NULL DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
  `SBL_CAMPO1` varchar(255) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Campo Dado Adicional 1',
  `SBL_CAMPO2` varchar(255) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Campo Dado Adicional 2',
  `SBL_CAMPO3` varchar(255) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Campo Dado Adicional 3',
  `SBL_CAMPO4` varchar(255) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Campo Dado Adicional 4',
  `SBL_CAMPO5` varchar(255) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Campo Dado Adicional 5',
  PRIMARY KEY (`SBL_ID`),
  KEY `IDSB2_Desc` (`SBL_IDSB2`,`SBL_Desc`),
  KEY `IDEA1` (`SBL_IDEA1`),
  KEY `IDSB1` (`SBL_IDSB1`),
  KEY `IDSB2` (`SBL_IDSB2`),
  KEY `IDSB2_DtEntrada` (`SBL_IDSB2`,`SBL_DtEntrada`),
  KEY `IDSD1` (`SBL_IDSD1`),
  KEY `IDSD2` (`SBL_IDSD2`)
);
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('SBL');
    }
};
