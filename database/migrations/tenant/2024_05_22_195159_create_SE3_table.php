<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
        CREATE TABLE `SE3` (
            `SE3_ID` int NOT NULL AUTO_INCREMENT,
            `SE3_IDEA1` int NOT NULL COMMENT 'Id da Empresa',
            `SE3_Doc` varchar(20) COLLATE latin1_general_ci DEFAULT '',
            `SE3_Emissao` datetime NOT NULL COMMENT 'data de emissao',
            `SE3_IDSA6Orig` int NOT NULL COMMENT ' ',
            `SE3_IDSA6Dest` int NOT NULL COMMENT ' ',
            `SE3_Valor` decimal(17,2) DEFAULT NULL COMMENT ' ',
            `SE3_Hist` varchar(255) COLLATE latin1_general_ci DEFAULT ' Transf',
            `SE3_CRIADOR` int unsigned NOT NULL COMMENT 'Id do Criador',
            `SE3_ALTERADOR` int unsigned NOT NULL DEFAULT '0' COMMENT 'Id do último alterador',
            `SE3_DT_INC` datetime NOT NULL COMMENT 'Data de inclusão do registro',
            `SE3_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteração do registro',
            `SE3_STATUS` int NOT NULL DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
            `SE3_NumSeq` int DEFAULT NULL COMMENT 'Numero Sequencial',
            PRIMARY KEY (`SE3_ID`),
            KEY `FK_SE3_SA6Orig` (`SE3_IDSA6Orig`),
            KEY `FK_SE3_SA6Dest` (`SE3_IDSA6Dest`)
          )
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('SE3');
    }
};
