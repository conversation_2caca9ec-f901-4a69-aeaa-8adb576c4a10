<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
        CREATE TABLE `SED` (
            `SED_ID` int NOT NULL AUTO_INCREMENT,
            `SED_IDEA1` int NOT NULL COMMENT 'Id da Empresa',
            `SED_IDSPDDes` int DEFAULT '0' COMMENT 'ID da Conta Sped de Despesa ou Compra',
            `SED_IDSPDRec` int DEFAULT '0' COMMENT 'ID da Conta Sped de Receita ou Faturamento',
            `SED_IDBL1Des` int DEFAULT '0' COMMENT 'ID da Linha do Balanco (Despesa)',
            `SED_IDBL1Rec` int DEFAULT '0' COMMENT 'ID da Linha do Balanco (Receita)',
            `SED_Tipo` varchar(1) COLLATE latin1_general_ci NOT NULL DEFAULT 'A' COMMENT 'Tipo da Natureza: D-Despesa, R-<PERSON><PERSON><PERSON>, I-Investimento',
            `SED_Desc` varchar(255) COLLATE latin1_general_ci DEFAULT NULL,
            `SED_Meta01` decimal(12,2) DEFAULT '0.00',
            `SED_Meta02` decimal(12,2) DEFAULT '0.00',
            `SED_Meta03` decimal(12,2) DEFAULT '0.00',
            `SED_Meta04` decimal(12,2) DEFAULT '0.00',
            `SED_Meta05` decimal(12,2) DEFAULT '0.00',
            `SED_Meta06` decimal(12,2) DEFAULT '0.00',
            `SED_Meta07` decimal(12,2) DEFAULT '0.00',
            `SED_Meta08` decimal(12,2) DEFAULT '0.00',
            `SED_Meta09` decimal(12,2) DEFAULT '0.00',
            `SED_Meta10` decimal(12,2) DEFAULT '0.00',
            `SED_Meta11` decimal(12,2) DEFAULT '0.00',
            `SED_Meta12` decimal(12,2) DEFAULT '0.00',
            `SED_Ativo` int DEFAULT '1' COMMENT '1=Ativo, 0=Inativo',
            `SED_CRIADOR` int unsigned NOT NULL DEFAULT '0' COMMENT 'Id do Criador',
            `SED_ALTERADOR` int unsigned NOT NULL DEFAULT '0' COMMENT 'Id do último alterador',
            `SED_DT_INC` datetime NOT NULL  COMMENT 'Data de inclusão do registro',
            `SED_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteração do registro',
            `SED_STATUS` int NOT NULL DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
            PRIMARY KEY (`SED_ID`),
            KEY `SED_Tipo` (`SED_IDEA1`,`SED_Tipo`,`SED_Desc`)
          )
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('SED');
    }
};
