<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
        CREATE TABLE `SD4` (
            `SD4_ID` int NOT NULL AUTO_INCREMENT,
            `SD4_IDEA1` int NOT NULL COMMENT 'Id da Empresa',
            `SD4_IDSB2_SBW_LOTE` int DEFAULT NULL COMMENT 'ID do Lote',
            `SD4_IDSBL` int DEFAULT NULL COMMENT 'ID do Lote',
            `SD4_IDSBW` int DEFAULT NULL COMMENT 'ID do Armazem',
            `SD4_IDSB1` int DEFAULT NULL,
            `SD4_IDSB2` int DEFAULT NULL,
            `SD4_IDSD1` int DEFAULT NULL,
            `SD4_IDSD2` int DEFAULT NULL,
            `SD4_IDSD3` int DEFAULT NULL,
            `SD4_IDSC6` int DEFAULT NULL COMMENT 'Id do Item do Orcamento no Lote',
            `SD4_IDSD4Ref` int DEFAULT NULL COMMENT 'ID do registro referenciado nos retornos de CCCDI',
            `SD4_Quant` decimal(11,3) NOT NULL COMMENT 'Quantidade',
            `SD4_Quant_2` decimal(11,3) DEFAULT '0.000' COMMENT 'Quantidade na 2a. UM',
            `SD4_PartNumber` varchar(20) COLLATE latin1_general_ci DEFAULT '0',
            `SD4_Campo1` longtext COLLATE latin1_general_ci,
            `SD4_Campo2` longtext COLLATE latin1_general_ci,
            `SD4_Campo3` longtext COLLATE latin1_general_ci,
            `SD4_Campo4` longtext COLLATE latin1_general_ci,
            `SD4_Campo5` longtext COLLATE latin1_general_ci,
            `SD4_CRIADOR` int unsigned DEFAULT NULL COMMENT 'Id do Criador',
            `SD4_ALTERADOR` int unsigned DEFAULT '0' COMMENT 'Id do Ãºltimo alterador',
            `SD4_DT_INC` datetime DEFAULT NULL COMMENT 'Data de inclusÃ£o do registro',
            `SD4_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteraÃ§Ã£o do registro',
            `SD4_STATUS` int DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
            PRIMARY KEY (`SD4_ID`),
            KEY `IDSB1` (`SD4_IDSB1`),
            KEY `IDSB2` (`SD4_IDSB2`),
            KEY `IDSD1` (`SD4_IDSD1`),
            KEY `IDSD2` (`SD4_IDSD2`),
            KEY `IDSD3` (`SD4_IDSD3`),
            KEY `IDSBW` (`SD4_IDSBW`),
            KEY `SD4_IDSB2_SBW_LOTE` (`SD4_IDSB2_SBW_LOTE`),
            KEY `FK_SD4_EA1` (`SD4_IDEA1`),
            KEY `FK_SD4_SC6` (`SD4_IDSC6`),
            KEY `IDSB2_IDSBL` (`SD4_IDSB2`,`SD4_IDSBL`),
            KEY `FK_SD4_SBL` (`SD4_IDSBL`),
            KEY `IDSB2_IDSBW_IDSBL` (`SD4_IDSB2`,`SD4_IDSBW`,`SD4_IDSBL`),
            KEY `IDEA1` (`SD4_IDEA1`),
            KEY `IDSD4Ref` (`SD4_IDSD4Ref`)
          )
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('SD4');
    }
};
