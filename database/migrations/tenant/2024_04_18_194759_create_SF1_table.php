<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
        CREATE TABLE `SF1` (
            `SF1_ID` int NOT NULL AUTO_INCREMENT,
            `SF1_IDEA1` int NOT NULL COMMENT 'Id da Empresa',
            `SF1_IDSF3` int DEFAULT '0',
            `SF1_IDSF1Ref` int DEFAULT '0' COMMENT 'ID da NF Referenciada',
            `SF1_IDSF2Ref` int DEFAULT '0' COMMENT 'ID da NF Referenciada',
            `SF1_IDMN2Nivel` int DEFAULT '0' COMMENT 'ID do Perfil para determinar o Nivel de permissao de acesso aos registros: 9-maximo, 0-minimo',
            `SF1_Chave_Acres_Desc` varchar(14) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Chave de Busca para registros de Acrescimo e Desconto',
            `SF1_Emissao` datetime NOT NULL COMMENT ' ',
            `SF1_HrEmissao` time DEFAULT '00:00:00' COMMENT 'Hora da Emissao',
            `SF1_EmissaoOriginal` datetime  COMMENT 'Emissao original, antes da alteracao pela baixa do titulo',
            `SF1_DtEntSai` datetime NOT NULL COMMENT 'Data de Entrada',
            `SF1_Doc` varchar(30) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Documento',
            `SF1_NumSeq` int DEFAULT '0' COMMENT 'Numero Sequencial',
            `SF1_NrNFe` int DEFAULT '0' COMMENT 'Numero sequencial da NFe',
            `SF1_IDSED` int NOT NULL COMMENT 'Id Natureza',
            `SF1_IDSA1` int NOT NULL DEFAULT '0' COMMENT 'Id Clientes/Fornecedores',
            `SF1_IDSA1Relacionado` int DEFAULT '0' COMMENT 'Id do Cliente Relacionado (utilizado para convenios)',
            `SF1_Hist` varchar(255) COLLATE latin1_general_ci NOT NULL COMMENT 'Histï¿½rico',
            `SF1_DifAliquota` varchar(255) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Msg Diferencial de Aliquota',
            `SF1_ValDifAliquota` decimal(18,2) DEFAULT '0.00' COMMENT 'Valor Diferencial de Aliquota',
            `SF1_IDSA1Customer` int DEFAULT '0',
            `SF1_OrderNumber` varchar(15) COLLATE latin1_general_ci DEFAULT '',
            `SF1_Sample` varchar(1) COLLATE latin1_general_ci DEFAULT '',
            `SF1_Currency` varchar(4) COLLATE latin1_general_ci DEFAULT '',
            `SF1_Incoterms` varchar(10) COLLATE latin1_general_ci DEFAULT '',
            `SF1_City` varchar(3) COLLATE latin1_general_ci DEFAULT '',
            `SF1_Terms` int DEFAULT '0',
            `SF1_Comission` decimal(4,1) DEFAULT '0.0',
            `SF1_DateRequested` datetime ,
            `SF1_CalculateComission` varchar(15) COLLATE latin1_general_ci DEFAULT '',
            `SF1_IDSA1Manufacturer` int DEFAULT '0',
            `SF1_PortOrigem` varchar(3) COLLATE latin1_general_ci DEFAULT '',
            `SF1_IDSA3Team` int DEFAULT '0',
            `SF1_IDSA3Seller1` int DEFAULT '0',
            `SF1_IDSA3Seller2` int DEFAULT '0',
            `SF1_IDSA3Seller3` int DEFAULT '0',
            `SF1_ProformInvoiceNumber` varchar(15) COLLATE latin1_general_ci DEFAULT '',
            `SF1_ProformInvoice` datetime ,
            `SF1_CPFCNPJ_CONT` varchar(150) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Codigo Especificador da Substituicao Tributaria',
            `SF1_CRIADOR` int unsigned NOT NULL COMMENT 'Id do Criador',
            `SF1_ALTERADOR` int unsigned NOT NULL DEFAULT '0' COMMENT 'Id do último alterador',
            `SF1_DT_INC` datetime NOT NULL COMMENT 'Data de inclusão do registro',
            `SF1_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteração do registro',
            `SF1_STATUS` int NOT NULL DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
            PRIMARY KEY (`SF1_ID`),
            KEY `SF1_Emissao` (`SF1_IDEA1`,`SF1_Emissao`),
            KEY `FK_SF1_SED` (`SF1_IDSED`),
            KEY `FK_SF1_SA1` (`SF1_IDSA1`),
            KEY `ID_SF1_Chave` (`SF1_IDEA1`,`SF1_Chave_Acres_Desc`),
            KEY `IDSF3` (`SF1_IDSF3`),
            KEY `FK_SF1_SF1` (`SF1_IDSF1Ref`),
            KEY `FK_SF1_SF2` (`SF1_IDSF2Ref`),
            KEY `IDSA1_NrNFe` (`SF1_IDSA1`,`SF1_NrNFe`)
          )
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('SF1');
    }
};
