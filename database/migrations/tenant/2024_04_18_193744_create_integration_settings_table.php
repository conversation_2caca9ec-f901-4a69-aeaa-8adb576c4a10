<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('integration_settings', function (Blueprint $table) {
            $table->id();
            $table->boolean('apr_assinaturas')->default(false);
            $table->boolean('apr_processos')->default(false);
            $table->boolean('CFO')->default(false);
            $table->boolean('CRM_EMP')->default(false);
            $table->boolean('CRM_OP')->default(false);
            $table->boolean('CRM_PR')->default(false);
            $table->boolean('EA1')->default(false);
            $table->boolean('EA2')->default(false);
            $table->boolean('EA3')->default(false);
            $table->boolean('MVA')->default(false);
            $table->boolean('NCM')->default(false);
            $table->boolean('SA1_SA3')->default(false);
            $table->boolean('SA1')->default(false);
            $table->boolean('SA3')->default(false);
            $table->boolean('SA4')->default(false);
            $table->boolean('SA6')->default(false);
            $table->boolean('SAE')->default(false);
            $table->boolean('SB1')->default(false);
            $table->boolean('SB2_SBW')->default(false);
            $table->boolean('SB2')->default(false);
            $table->boolean('SBA')->default(false);
            $table->boolean('SBL')->default(false);
            $table->boolean('SBW')->default(false);
            $table->boolean('SC2')->default(false);
            $table->boolean('SC3')->default(false);
            $table->boolean('SC4')->default(false);
            $table->boolean('SC5')->default(false);
            $table->boolean('SC6')->default(false);
            $table->boolean('SC7')->default(false);
            $table->boolean('SCA')->default(false);
            $table->boolean('SD1')->default(false);
            $table->boolean('SD2')->default(false);
            $table->boolean('SD3')->default(false);
            $table->boolean('SD4')->default(false);
            $table->boolean('SED')->default(false);
            $table->boolean('SEP')->default(false);
            $table->boolean('SE2')->default(false);
            $table->boolean('SF1')->default(false);
            $table->boolean('SF2')->default(false);
            $table->boolean('SF3')->default(false);
            $table->boolean('SG0')->default(false);
            $table->boolean('SG1')->default(false);
            $table->boolean('spx38940012')->default(false);
            $table->boolean('spx38940016')->default(false);
            $table->boolean('spx38940018')->default(false);
            $table->boolean('spx38940019')->default(false);
            $table->boolean('spx38940036')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('integration_settings');
    }
};
