<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
        CREATE TABLE `SC6` (
          `SC6_ID` int NOT NULL AUTO_INCREMENT,
          `SC6_IDEA1` int NOT NULL,
          `SC6_IDSC5` int NOT NULL COMMENT 'Cabecalho do Pedido/Orcamento',
          `SC6_IDSB1` int NOT NULL COMMENT 'Id do produto',
          `XXX_IDSB1Old` int DEFAULT NULL,
          `SC6_IDSB2` int DEFAULT '0',
          `SC6_IDSBW` int DEFAULT '0' COMMENT 'ID do Armazem',
          `SC6_IDSB1_SBK` int DEFAULT '0' COMMENT 'Equivale ao SBK_IDSB1',
          `SC6_IDSB2_SBK` int DEFAULT '0' COMMENT 'Equivale ao SBK_IDSB2',
          `SC6_IDSC2` int DEFAULT '0',
          `SC6_IDSB1_UMComercial` int DEFAULT '0' COMMENT 'ID da Unid.Medida Comercial (que vai na nota)',
          `SC6_IDSA1` int DEFAULT '0' COMMENT 'Id Cliente/Fornecedor',
          `SC6_RP_FORMAPAGAMENTO` int DEFAULT '0' COMMENT 'Id da Forma de Pagamento que é ativado no parametro SX6',
          `SC6_QuantDiarias` decimal(10,3) DEFAULT '1.000' COMMENT 'Quantidade de Diarias',
          `SC6_Quant` decimal(10,3) DEFAULT NULL COMMENT 'Quantidade do item no ERPFlex',
          `SC6_QuantSeparada` decimal(10,3) DEFAULT '0.000' COMMENT 'Quantidade Separada',
          `SC6_DtSeparacao` datetime DEFAULT NULL COMMENT 'Data e Hora da Separação',
          `SC6_PrcUni` decimal(18,8) DEFAULT '0.00000000',
          `SC6_ValItem` decimal(11,2) DEFAULT NULL COMMENT 'Valor do Item',
          `SC6_QuantComercial` decimal(11,3) DEFAULT '0.000' COMMENT 'Qt. Comercial (que vai na nota)',
          `SC6_QuantTeorica` decimal(10,3) DEFAULT '0.000' COMMENT 'Quantidade teorica, preenchida qdo ocorrer pesagem real',
          `SC6_QuantVol` decimal(10,3) DEFAULT '0.000' COMMENT 'Quantidade de volumes',
          `SC6_QuantVolSeparada` decimal(10,3) DEFAULT '0.000' COMMENT 'Quantidade separada de volumes',
          `SC6_PrcUniComercial` decimal(18,8) DEFAULT '0.00000000' COMMENT 'Preco Unit. Comercial',
          `SC6_SitTribICMS` varchar(3) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Sit. Trib. ICMS',
          `SC6_PICMS` decimal(4,2) DEFAULT '0.00' COMMENT '% ICMS',
          `SC6_ValICMS` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do ICMS',
          `SC6_PIPI` decimal(4,2) DEFAULT '0.00' COMMENT '% IPI',
          `SC6_ValIPI` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do IPI',
          `SC6_ValICMS_ST` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor ICMS-ST',
          `SC6_ValFCP_ST` decimal(10,2) DEFAULT '0.00',
          `SC6_ValJuros` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor dos juros financeiros de parcelamento rateados por item',
          `SC6_ValFrete` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor rateado do frete',
          `SC6_Fixo` varchar(1) COLLATE latin1_general_ci NOT NULL DEFAULT 'V' COMMENT 'Fixo ou Variável',
          `SC6_Custo` decimal(16,8) DEFAULT '0.00000000' COMMENT 'Custo aplicado para compor o preço unitário do item',
          `SC6_PrazoEntrega` int NOT NULL DEFAULT '0',
          `SC6_NumPed` varchar(15) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Número do pedido de compra',
          `SC6_ItemPed` varchar(6) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Item do pedido de compra',
          `SC6_Obs` text COLLATE latin1_general_ci COMMENT 'Dados adicionais',
          `SC6_ModoObs` varchar(1) COLLATE latin1_general_ci DEFAULT '0' COMMENT 'Obs no DANFE: 0-Nao levar, 1-Substituir a Desc.Prod., 2-Concatenar com a Desc.Prod.',
          `SC6_TipoCalculo` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Tipo de cÃ¡lculo que serÃ¡ aplicado na quantidade sendo P - PerÃ­metro e A - Ã?rea',
          `SC6_LarguraProducao` decimal(18,8) DEFAULT '0.00000000' COMMENT 'Largura a considerar na produÃ§Ã£o do produto',
          `SC6_AlturaProducao` decimal(18,8) DEFAULT '0.00000000' COMMENT 'Altura a considerar na produÃ§Ã£o do produto',
          `SC6_LarguraPeca` decimal(18,8) DEFAULT '0.00000000' COMMENT 'Largura da peÃ§a a ser considerada no cÃ¡lculo da quantidade',
          `SC6_AlturaPeca` decimal(18,8) DEFAULT '0.00000000' COMMENT 'Altura da peÃ§a a ser considerada no cÃ¡lculo da quantidade',
          `SC6_Reserva` decimal(18,8) DEFAULT '0.00000000' COMMENT 'Qtd reservada do Produto',
          `SC6_Senha` int DEFAULT NULL COMMENT 'Senha digitada pelo Cliente/Fornecedor',
          `SC6_CRIADOR` int unsigned NOT NULL COMMENT 'Id do Criador',
          `SC6_ALTERADOR` int unsigned NOT NULL DEFAULT '0' COMMENT 'Id do último alterador',
          `SC6_DT_INC` datetime NOT NULL COMMENT 'Data de inclusão do registro',
          `SC6_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteração do registro',
          `SC6_STATUS` int NOT NULL DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
          `SC6_ValDesconto` decimal(10,2) DEFAULT '0.00' COMMENT 'Desconto do item',
          `SC6_ValDespesa` decimal(10,2) DEFAULT '0.00' COMMENT 'Despesas acessorias do item',
          `SC6_IncluirDescBaseICMS` varchar(1) COLLATE latin1_general_ci DEFAULT '0' COMMENT 'Incluir Desconto na BC do ICMS: 0-Nao, 1-Sim',
          `SC6_IncluirDescBaseICMS_ST` varchar(1) COLLATE latin1_general_ci DEFAULT '0' COMMENT 'Incluir Desconto na BC do ICMS_ST: 0-Nao, 1-Sim',
          `SC6_Sequencia` int DEFAULT NULL COMMENT 'Sequencia Item',
          `SC6_IncluirValFreteBaseIPI` varchar(1) COLLATE latin1_general_ci DEFAULT '0',
          `SC6_IncluirValDescontoBaseIPI` varchar(1) COLLATE latin1_general_ci DEFAULT '0',
          `SC6_IncluirValFreteBaseICMS` varchar(1) COLLATE latin1_general_ci DEFAULT '0',
          `SC6_IncluirValFreteBaseICMS_ST` varchar(1) COLLATE latin1_general_ci DEFAULT '0',
          `SC6_IncluirValDespesaBaseICMS` varchar(1) COLLATE latin1_general_ci DEFAULT '0',
          `SC6_IncluirValDespesaBaseICMS_ST` varchar(1) COLLATE latin1_general_ci DEFAULT '0',
          `SC6_IncluirValDespesaBaseIPI` varchar(1) COLLATE latin1_general_ci DEFAULT '0',
          `SC6_SD2_Quant` decimal(11,3) DEFAULT NULL COMMENT 'Quantidade faturada',
          `SC6_SD2_QuantComercial` decimal(11,3) DEFAULT NULL COMMENT 'Quantidade Comercial faturada',
          `SC6_SD2_QuantVol` decimal(10,3) DEFAULT NULL COMMENT 'Quantidade de volumes faturada',
          `SC6_SD2_ValItem` decimal(11,2) DEFAULT NULL COMMENT 'Valor faturado',
          `SC6_SD2_ValItemNFe` decimal(11,2) DEFAULT NULL COMMENT 'Valor faturado',
          `SC6_SD2_ValICMS_STBruto` decimal(11,2) DEFAULT NULL COMMENT 'Valor ICMS-ST faturado',
          `SC6_SD2_ValFrete` decimal(11,2) DEFAULT NULL COMMENT 'Valor Frete faturado',
          `SC6_DataEntrega` date DEFAULT NULL COMMENT 'Data de Entrega',
          `SC6_Obs_2` text COLLATE latin1_general_ci COMMENT 'Campo Observacao 2 para ser utilizado como Dados Ordem de Produção:',
          PRIMARY KEY (`SC6_ID`),
          KEY `SC6_SC5` (`SC6_IDSC5`),
          KEY `FK_SC6_SB1` (`SC6_IDSB1`),
          KEY `FK_SC6_SB2` (`SC6_IDSB2`),
          KEY `FK_SC6_SB1_SBK` (`SC6_IDSB1_SBK`),
          KEY `FK_SC6_SB2_SBK` (`SC6_IDSB2_SBK`),
          KEY `FK_SC6_SBW` (`SC6_IDSBW`),
          KEY `FK_SC6_SA1` (`SC6_IDSA1`),
          KEY `SC6_IDEA1` (`SC6_IDEA1`),
          KEY `SC6_ItemPed` (`SC6_ItemPed`)
        );
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('SC6');
    }
};
