<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
        CREATE TABLE `SC5` (
            `SC5_ID` int NOT NULL AUTO_INCREMENT,
            `SC5_IDEA1` int NOT NULL COMMENT 'Id da Empresa',
            `SC5_Emissao` datetime NOT NULL COMMENT ' ',
            `SC5_Doc` varchar(30) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Documento',
            `SC5_NumSeq` int DEFAULT '0' COMMENT 'Numero Sequencial',
            `SC5_IDSA1` int NOT NULL COMMENT 'Id Cliente/Fornecedor',
            `SC5_IDSA1Relacionado` int DEFAULT '0' COMMENT 'Id do Cliente Relacionado (utilizado para convenios)',
            `SC5_IDSA1Homenageado` int DEFAULT '0' COMMENT 'Id Cliente relacionado (utilizado para orçamento de buffet) e atendido (utilizado nas vendas agenciadas)',
            `SC5_IDSAQ` int DEFAULT '0' COMMENT 'ID do Contato do cliente',
            `SC5_IDSA3` int NOT NULL COMMENT 'Id Vendedor',
            `SC5_IDSA4` int DEFAULT '0' COMMENT 'ID da Transportador',
            `SC5_IDSA4Redespacho` int DEFAULT '0' COMMENT 'Id da segunda Transportadora',
            `SC5_IDSB1` int DEFAULT '0' COMMENT 'ID do Produto ou Servico',
            `SC5_IDSB2` int DEFAULT '0',
            `SC5_IDSBW` int DEFAULT '0' COMMENT 'ID do Armazem',
            `SC5_IDSC2` int NOT NULL,
            `SC5_IDSA1Entrega` int DEFAULT '0' COMMENT 'Id do cliente de entrega',
            `SC5_IDTPRECO` int DEFAULT NULL COMMENT 'ID da tabela de preço',
            `SC5_IDPF1` int DEFAULT NULL COMMENT 'ID da propriedade fiscal',
            `SC5_DtIniExec` datetime  COMMENT 'Data Inicial da Execucao',
            `SC5_DtFinExec` datetime  COMMENT 'Data Final da Execucao',
            `SC5_ValConvExced` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do Convidade Excedente',
            `SC5_ValBebExced` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor da Bebida Excedente',
            `SC5_Quant1` decimal(7,0) DEFAULT '0' COMMENT 'Quant. Adultos',
            `SC5_Quant2` decimal(7,0) DEFAULT '0' COMMENT 'Quant. Adolescentes',
            `SC5_Quant3` decimal(7,0) DEFAULT '0' COMMENT 'Quant. Crianças',
            `SC5_Fator1` decimal(3,1) DEFAULT '0.0' COMMENT 'Fator de Conversão Adulto',
            `SC5_Fator2` decimal(3,1) DEFAULT '0.0' COMMENT 'Fator de Conversão Adolescente',
            `SC5_Fator3` decimal(3,1) DEFAULT '0.0' COMMENT 'Fator de Conversão Criança',
            `SC5_Quant` decimal(7,2) DEFAULT '0.00' COMMENT 'Quant. Total',
            `SC5_QuantPre` decimal(7,2) DEFAULT '0.00',
            `SC5_QuantPos` decimal(7,2) DEFAULT '0.00',
            `SC5_QuantVol` decimal(10,3) DEFAULT '0.000' COMMENT 'Quantidade de volumes',
            `SC5_QuantVolSeparada` decimal(10,3) DEFAULT '0.000' COMMENT 'Quantidade separada de volumes',
            `SC5_Valor` decimal(11,2) DEFAULT '0.00' COMMENT 'Valor Total do Orçamento',
            `SC5_ValDesconto` decimal(10,2) DEFAULT '0.00' COMMENT 'Desconto total',
            `SC5_ModFrete` varchar(1) CHARACTER SET latin1 DEFAULT '' COMMENT 'Modalidade do frete\n\n0=Contratação do Frete por conta do Remetente (CIF);\n1=Contratação do Frete por conta do Destinatário (FOB);\n2=Contratação do Frete por conta de Terceiros;\n3=Transporte Próprio por conta do Remetente;\n4=Transporte Próprio por conta do Destinatário;\n9=Sem Ocorrência de Transporte.',
            `SC5_TipoDesconto` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT 'P - para Percentual, V - Para Valor Desconto no Cabeçalho do Orcamento',
            `SC5_ValFrete` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor Total do Frete',
            `SC5_CodigoFrete` varchar(5) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Tipo Frete',
            `SC5_ValJuros` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT 'Valor do Juros do OrÃ§amento',
            `SC5_TabelaPreco` int DEFAULT NULL COMMENT 'Numero da Tabela de Preco',
            `SC5_Hist` varchar(255) COLLATE latin1_general_ci NOT NULL COMMENT 'Histï¿½rico',
            `SC5_Hist_Romaneio` varchar(255) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Historico Romaneio',
            `SC5_HistExp` varchar(255) COLLATE latin1_general_ci DEFAULT NULL,
            `SC5_DtVencto` datetime DEFAULT NULL COMMENT 'Data de vencimento do orçamento',
            `SC5_DtPrevFat` datetime DEFAULT NULL COMMENT 'Data de previsão do faturamento',
            `SC5_DtAprovCli` datetime  COMMENT 'Data da aprovacao pelo Cliente',
            `SC5_PesoLiquido` decimal(13,5) DEFAULT NULL,
            `SC5_PesoBruto` decimal(13,5) DEFAULT NULL,
            `SC5_PrazoEntrega` int NOT NULL DEFAULT '0',
            `SC5_Campo1` longtext COLLATE latin1_general_ci,
            `SC5_Campo2` longtext COLLATE latin1_general_ci,
            `SC5_Campo3` longtext COLLATE latin1_general_ci,
            `SC5_Campo4` longtext COLLATE latin1_general_ci,
            `SC5_Campo5` longtext COLLATE latin1_general_ci,
            `SC5_Campo6` longtext COLLATE latin1_general_ci,
            `SC5_Campo7` longtext COLLATE latin1_general_ci,
            `SC5_Campo8` longtext COLLATE latin1_general_ci,
            `SC5_Campo9` longtext COLLATE latin1_general_ci,
            `SC5_Campo10` longtext COLLATE latin1_general_ci,
            `SC5_Campo11` longtext COLLATE latin1_general_ci,
            `SC5_Campo12` longtext COLLATE latin1_general_ci,
            `SC5_Campo13` longtext COLLATE latin1_general_ci,
            `SC5_Campo14` longtext COLLATE latin1_general_ci,
            `SC5_Campo15` longtext COLLATE latin1_general_ci,
            `SC5_Campo16` longtext COLLATE latin1_general_ci,
            `SC5_Campo17` longtext COLLATE latin1_general_ci,
            `SC5_Campo18` longtext COLLATE latin1_general_ci,
            `SC5_Campo19` longtext COLLATE latin1_general_ci,
            `SC5_Campo20` longtext COLLATE latin1_general_ci,
            `SC5_Campo21` longtext COLLATE latin1_general_ci,
            `SC5_Campo22` longtext COLLATE latin1_general_ci,
            `SC5_Campo23` longtext COLLATE latin1_general_ci,
            `SC5_Campo24` longtext COLLATE latin1_general_ci,
            `SC5_Campo25` longtext COLLATE latin1_general_ci,
            `SC5_Campo26` longtext COLLATE latin1_general_ci,
            `SC5_Situacao` varchar(20) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Situacao do Orcamento: Aberto/Fechado',
            `SC5_StatusAprov` varchar(1) COLLATE latin1_general_ci DEFAULT '2' COMMENT 'Status da aprova��o do Or�amento, sendo: 1-Aprovado Pelo Cliente; 2-Aguardando Aprova��o 3-Rejeitado pelo Cliente',
            `SC5_EnviadoZanthus` varchar(1) COLLATE latin1_general_ci DEFAULT 'S' COMMENT 'Indica se ja foi enviado ao Sistema Zanthus',
            `SC5_HrBloqueio` datetime DEFAULT NULL COMMENT 'Hora de Bloqueio',
            `SC5_Embarque` int DEFAULT '0' COMMENT 'Qtd de Embarques (Vezes que foi Faturado este Orcamento)',
            `SC5_PedidoCompra` varchar(15) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Numero do Pedido de Compra',
            `SC5_StatusReserva` varchar(1) COLLATE latin1_general_ci DEFAULT '0' COMMENT 'Status da Reserva do Orcamento, sendo: 0 - Sem Reserva; 1-Reservado Completamente',
            `SC5_IDEA2Reservando` int DEFAULT NULL COMMENT 'ID do Usuario que esta efetuando a Reserva Manual',
            `SC5_CRIADOR` int unsigned NOT NULL COMMENT 'Id do Criador',
            `SC5_ALTERADOR` int unsigned NOT NULL DEFAULT '0' COMMENT 'Id do último alterador',
            `SC5_DT_INC` datetime NOT NULL COMMENT 'Data de inclusão do registro',
            `SC5_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteração do registro',
            `SC5_STATUS` int NOT NULL DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
            `SC5_PercDesconto` decimal(10,2) DEFAULT '0.00' COMMENT 'Percentual de desconto',
            `SC5_ValorTotal` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor total do Orçamento/Pedido de Venda',
            `SC5_StatusSeparacao` varchar(30) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Status da Separação',
            `SC5_TemAgenda` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Possui agendamento (S/N)',
            `SC5_Faturado` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Faturado (S/N) - Somente para orcamento avançado',
            `SC5_SC6Faturado` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Itens faturados (S/N)',
            `SC5_IndFinal` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT '0 - Normal - 1 Consumidor Final',
            `SC5_SD2_Quant` decimal(11,3) DEFAULT NULL COMMENT 'Quantidade faturada',
            `SC5_SD2_QuantComercial` decimal(11,3) DEFAULT NULL COMMENT 'Quantidade Comercial faturada',
            `SC5_SD2_QuantVol` decimal(10,3) DEFAULT NULL COMMENT 'Quantidade de volumes faturada',
            `SC5_SD2_ValItem` decimal(11,2) DEFAULT NULL COMMENT 'Valor faturado',
            `SC5_SD2_ValItemNFe` decimal(11,2) DEFAULT NULL COMMENT 'Valor faturado',
            `SC5_SD2_ValICMS_STBruto` decimal(11,2) DEFAULT NULL COMMENT 'Valor ICMS-ST faturado',
            `SC5_SD2_ValFrete` decimal(11,2) DEFAULT NULL COMMENT 'Valor Frete faturado',
            `SC5_IDSEP` varchar(10) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'ID SEP',
            PRIMARY KEY (`SC5_ID`),
            KEY `FK_SC5_SA1` (`SC5_IDSA1`),
            KEY `FK_SC5_SA1Homenageado` (`SC5_IDSA1Homenageado`),
            KEY `FK_SC5_SA3` (`SC5_IDSA3`),
            KEY `FK_SC5_SB1` (`SC5_IDSB1`),
            KEY `FK_SC5_SB2` (`SC5_IDSB2`),
            KEY `FK_SC5_SA1Entrega` (`SC5_IDSA1Entrega`),
            KEY `FK_SC5_SBW` (`SC5_IDSBW`),
            KEY `IDEA1_Doc` (`SC5_IDEA1`,`SC5_Doc`),
            KEY `IDEA1_Emissao` (`SC5_IDEA1`,`SC5_Emissao`),
            KEY `IDSC2` (`SC5_IDSC2`),
            KEY `FK_SC5_TPRECO_idx` (`SC5_IDTPRECO`)
          )
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('SC5');
    }
};
