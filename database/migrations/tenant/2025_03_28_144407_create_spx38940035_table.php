<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
            CREATE TABLE `spx38940035` (
              `id` int NOT NULL AUTO_INCREMENT,
              `link` int DEFAULT NULL,
              `cpo001` date DEFAULT NULL,
              `cpo002` varchar(8) DEFAULT NULL,
              `cpo003` varchar(15) DEFAULT NULL,
              `cpo004` varchar(15) DEFAULT NULL,
              `cpo005` varchar(50) DEFAULT NULL,
              `cpo006` int DEFAULT NULL,
              `cpo007` int DEFAULT NULL,
              `cpo008` date DEFAULT NULL,
              `cpo009` longblob,
              `cpo010` double DEFAULT NULL,
              PRIMARY KEY (`id`),
              KEY `link` (`link`)
            )
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('spx38940035');
    }
};
