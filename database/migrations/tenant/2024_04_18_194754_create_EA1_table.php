<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
CREATE TABLE `EA1` (
  `EA1_ID` int NOT NULL AUTO_INCREMENT,
  `EA1_Empresa` int DEFAULT NULL COMMENT 'Código da empresa',
  `EA1_IDEA7` int DEFAULT '0' COMMENT 'Id do Database utilizado para a empresa',
  `EA1_IDSAE` int DEFAULT '0',
  `EA1_IDSAM` int DEFAULT '0',
  `EA1_IDSA1` int DEFAULT NULL COMMENT 'ID Cliente Associado',
  `EA1_Filial` int DEFAULT '0' COMMENT 'Código da filial',
  `EA1_Modalidade` int NOT NULL DEFAULT '0' COMMENT '<PERSON><PERSON> da Modadlidade de Comercialização',
  `EA1_RazaoSocial` varchar(65) COLLATE latin1_general_ci NOT NULL COMMENT 'Razão Social da empresa',
  `EA1_NomeFantasia` varchar(60) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Nome Fantasia da Empresa',
  `EA1_End` varchar(50) COLLATE latin1_general_ci NOT NULL COMMENT ' ',
  `EA1_Numero` varchar(10) COLLATE latin1_general_ci NOT NULL,
  `EA1_Complemento` varchar(60) COLLATE latin1_general_ci DEFAULT '' COMMENT 'IE do Substituto Tributario',
  `EA1_Tel` varchar(13) COLLATE latin1_general_ci NOT NULL COMMENT ' ',
  `EA1_Fax` varchar(13) COLLATE latin1_general_ci NOT NULL COMMENT ' ',
  `EA1_Contato` varchar(30) COLLATE latin1_general_ci NOT NULL COMMENT ' ',
  `EA1_EMail` varchar(60) COLLATE latin1_general_ci NOT NULL COMMENT ' ',
  `EA1_Bairro` varchar(30) COLLATE latin1_general_ci NOT NULL COMMENT ' ',
  `EA1_Mun` varchar(20) COLLATE latin1_general_ci NOT NULL COMMENT ' ',
  `EA1_Est` varchar(2) COLLATE latin1_general_ci NOT NULL COMMENT ' ',
  `EA1_CEP` varchar(8) COLLATE latin1_general_ci NOT NULL COMMENT ' ',
  `EA1_CNPJ` varchar(14) COLLATE latin1_general_ci NOT NULL COMMENT ' ',
  `EA1_Inscr` varchar(14) COLLATE latin1_general_ci NOT NULL COMMENT 'Incricao Estadual',
  `EA1_InscrM` varchar(14) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Incricao Municipal',
  `EA1_IEST` varchar(14) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Complemento do endereco',
  `EA1_CNAE` varchar(7) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'CNAE',
  `EA1_Tipo` varchar(2) COLLATE latin1_general_ci DEFAULT '00' COMMENT 'Tipo da Empresa',
  `EA1_Regime` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Regime de Tributacao: 1-Simples Nacional, 2-Simples Nacional-excesso de sublimite de receita bruta, 3-Lucro Presumido, 4-Lucro Real',
  `EA1_IncidenciaPISCOFINS` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Regime de Incidencia do PIS/COFINS: 1-Não-Cumulativo,2-Cumulativo,3-Cumulativo/Não-Cumulativo,',
  `EA1_ApuracaoICMSST` varchar(1) COLLATE latin1_general_ci DEFAULT NULL,
  `EA1_BaseCalcPartICMSST` varchar(1) COLLATE latin1_general_ci DEFAULT '1' COMMENT 'Tipo da base de calculo do ICMS-ST para partilha interestadual: 1-Simples, 2-Dupla',
  `EA1_CredICMSST` varchar(1) COLLATE latin1_general_ci DEFAULT '1' COMMENT 'Identifica se a empresa toma crédito de ICMS-ST das notas de compra: 0-não / 1-Sim',
  `EA1_DesoneracaoFolha` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Desoneração da Folha: 0-Nenhuma,1-Total,2-Parcial',
  `EA1_AliqPrev` decimal(4,2) DEFAULT '0.00' COMMENT 'Alíquota da contribuição previdenciária sobre a receita bruta',
  `EA1_Aliq` decimal(4,2) DEFAULT '0.00' COMMENT 'Aliquota de Imposto do Simples/LP',
  `EA1_BaseIRPJ` decimal(4,2) DEFAULT '0.00' COMMENT '% Receita Bruta para Base Calculo IRPJ',
  `EA1_BaseCSLL` decimal(4,2) DEFAULT '0.00' COMMENT '% Receita Bruta para Base Calculo do CSLL',
  `EA1_SUFRAMA` varchar(9) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Inscricao na SUFRAMA',
  `EA1_RNTRC` varchar(8) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Registro Nacional de Transportadores Rodoviários de Carga (MDFe)',
  `EA1_tpEmit` int DEFAULT NULL COMMENT 'Tipo do Emitente (MDFe): 1-Prestador de serviço de transporte 2-Transporte de Carga Própria 3-Prestador de serviço de transporte que emitirá CT-e',
  `EA1_tpTransp` int DEFAULT NULL COMMENT 'Tipo do Transportador (MDFe): 1-ETC 2-TAC 3-CTC',
  `EA1_NomeSignatario` varchar(100) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Nome do Signatário',
  `EA1_CPFSignatario` varchar(11) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Número de inscrição do signatário no CPF',
  `EA1_CodQualifSignatario` varchar(3) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Código de qualificação do signatário',
  `EA1_NomeCont` varchar(100) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Nome do contabilista',
  `EA1_CPFCont` varchar(11) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Número de inscrição do contabilista no CPF',
  `EA1_CodQualifCont` varchar(3) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Código de qualificação do contabilista',
  `EA1_CRCCont` varchar(15) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Número de inscrição do contabilista no conselho regional de contabilidade',
  `EA1_CNPJCont` varchar(14) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Número de inscrição do escritório de contabilidade no CNPJ',
  `EA1_CEPCont` varchar(8) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Código de endereçamento postal do contabilista',
  `EA1_EndCont` varchar(60) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Logradouro e endereço do imóvel do contabilista',
  `EA1_NumeroCont` varchar(10) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Número do imóvel do contabilista',
  `EA1_ComplementoCont` varchar(60) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Dados complementares do endereço do contabilista',
  `EA1_BairroCont` varchar(60) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Bairro em que o imóvel do contabilista está situado',
  `EA1_TelCont` varchar(11) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Número do telefone do contabilista (DDD + fone)',
  `EA1_FaxCont` varchar(11) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Número do Fax do contabilista',
  `EA1_EMailCont` varchar(120) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Endereço do correio eletrônico do contabilista',
  `EA1_CodMunCont` varchar(7) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Código do munícipio do contabilista, conforme tabela IBGE',
  `EA1_Foto` varchar(20) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Foto da empresa',
  `EA1_CRIADOR` int unsigned NOT NULL COMMENT 'Id do Criador',
  `EA1_ALTERADOR` int unsigned NOT NULL DEFAULT '0' COMMENT 'Id do último alterador',
  `EA1_DT_INC` datetime NOT NULL COMMENT 'Data de inclusão do registro',
  `EA1_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteração do registro',
  `EA1_STATUS` int NOT NULL DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
  `EA1_Enabled` int DEFAULT '1',
  `EA1_DtExpiracao` datetime DEFAULT NULL,
  `EA1_Usuarios` int DEFAULT '999',
  `EA1_IDEA0` int DEFAULT NULL,
  `EA1_EstCont` varchar(2) COLLATE latin1_general_ci DEFAULT NULL COMMENT ' ',
  `EA1_Perfil` int DEFAULT '1' COMMENT 'Perfil  1=A, 2=B e 3=C',
  `EA1_Atv` int DEFAULT '0' COMMENT 'Indicador de tipo de atividade:0 – Industrial ou equiparado a industrial 1 – Outros.',
  `EA1_Licenca` int DEFAULT '1' COMMENT 'Tipo licença: 0: SMALL 1: FULL',
  PRIMARY KEY (`EA1_ID`)
);
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('EA1');
    }
};
