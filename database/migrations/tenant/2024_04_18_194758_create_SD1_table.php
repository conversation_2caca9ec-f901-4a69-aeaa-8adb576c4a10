<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
        CREATE TABLE `SD1` (
            `SD1_ID` int NOT NULL AUTO_INCREMENT,
            `SD1_IDEA1` int NOT NULL COMMENT 'Id da empresa',
            `SD1_IDSF1` int NOT NULL COMMENT ' ',
            `SD1_IDSB1` int NOT NULL COMMENT ' ',
            `SD1_IDSB2` int DEFAULT '0',
            `SD1_IDSBW` int DEFAULT '0' COMMENT 'ID do Armazem',
            `SD1_IDSED` int NOT NULL DEFAULT '0',
            `SD1_IDSC2` int NOT NULL DEFAULT '0',
            `SD1_IDSC4` int DEFAULT '0',
            `SD1_IDCFO` int DEFAULT NULL,
            `SD1_IDSF1GUIA` int DEFAULT NULL COMMENT 'ID da Guia que recolheu o imposto',
            `SD1_IDSF1DARF` int DEFAULT NULL COMMENT 'ID da Guia que recolheu o imposto',
            `SD1_IDSB1_UMComercial` int DEFAULT '0' COMMENT 'ID da Unid.Medida Comercial (que vem na nota)',
            `SD1_Doc` varchar(30) COLLATE latin1_general_ci DEFAULT '',
            `SD1_TipoMovto` varchar(3) COLLATE latin1_general_ci DEFAULT NULL,
            `SD1_Quant` decimal(11,3) DEFAULT '0.000' COMMENT 'Qt. no ERPFlex',
            `SD1_PrcUni` decimal(18,8) DEFAULT '0.00000000' COMMENT 'Preco Unit. no ERPFlex',
            `SD1_ValItem` decimal(11,2) DEFAULT '0.00',
            `SD1_ValItemNFe` decimal(11,2) DEFAULT '0.00' COMMENT 'Valor na NFe',
            `SD1_QuantComercial` decimal(11,3) DEFAULT '0.000' COMMENT 'Qt. Comercial (que vem na nota)',
            `SD1_PrcUniComercial` decimal(18,8) DEFAULT '0.00000000' COMMENT 'Preco Unit. Comercial',
            `SD1_Custo` decimal(11,2) DEFAULT '0.00',
            `SD1_ValFrete` decimal(10,2) DEFAULT '0.00' COMMENT 'Frete do item',
            `SD1_ValSeguro` decimal(10,2) DEFAULT '0.00' COMMENT 'Seguro do item',
            `SD1_ValDesconto` decimal(10,2) DEFAULT '0.00' COMMENT 'Desconto do item',
            `SD1_ValDespesa` decimal(10,2) DEFAULT '0.00' COMMENT 'Despesas acessorias do item',
            `SD1_ValJuros` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor dos juros financeiros de parcelamento rateados por item',
            `SD1_SitTribISS` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT '1=tributa;2=nao tributa',
            `SD1_PCredSimples` decimal(4,2) DEFAULT '0.00' COMMENT 'Alíquota aplicável de cálculo do crédito',
            `SD1_CredICMSSimples` decimal(10,2) DEFAULT '0.00' COMMENT 'Crédito do ICMS que pode ser aproveitado',
            `SD1_PMargemICMS_STRet` decimal(5,2) DEFAULT '0.00' COMMENT '% Margem de valor adicional ICMS ST retido anteriormente',
            `SD1_BaseICMS_STRet` decimal(11,2) DEFAULT '0.00' COMMENT 'BC ICMS ST retido anteriormente',
            `SD1_PST` decimal(7,4) DEFAULT '0.0000' COMMENT 'Alíquota suportada pelo Consumidor Final',
            `SD1_ValICMS_STRet` decimal(10,2) DEFAULT '0.00' COMMENT 'ICMS ST retido anteriormente',
            `SD1_BaseFCPSTRet` double(15,2) DEFAULT '0.00' COMMENT 'Valor da Base de Cálculo do FCP retido anteriormente por ST',
            `SD1_PFCPSTRet` decimal(7,4) DEFAULT '0.0000' COMMENT 'Percentual do FCP retido anteriormente por Substituição Tributária',
            `SD1_ValFCPSTRet` double(15,2) DEFAULT '0.00' COMMENT 'Valor do FCP retido por Substituição Tributária',
            `SD1_PRedBaseCalcEfet` decimal(4,2) DEFAULT NULL COMMENT 'Percentual de redução da base de cálculo efetiva',
            `SD1_ValBaseCalcEfet` decimal(10,2) DEFAULT NULL COMMENT 'Valor da base de cálculo efetiva',
            `SD1_PICMSEfet` decimal(4,2) DEFAULT NULL COMMENT 'Alíquota do ICMS efetivo',
            `SD1_ValICMSEfet` decimal(10,2) DEFAULT NULL COMMENT 'Valor do ICMS efetivo',
            `SD1_BaseICMS_STDestino` decimal(11,2) DEFAULT '0.00' COMMENT 'BC ICMS ST da UF destino',
            `SD1_ValICMS_STDestino` decimal(10,2) DEFAULT '0.00' COMMENT 'ICMS ST da UF destino',
            `SD1_CompoeTotalNF` varchar(1) COLLATE latin1_general_ci DEFAULT '1' COMMENT 'Valor do item compoe o total da Nota? 0-nao 1-sim',
            `SD1_SitTribICMS` varchar(3) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Sit. Trib. ICMS',
            `SD1_ModBaseICMS` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Modalid. de determ. da BC ICMS',
            `SD1_PRedBaseICMS` decimal(9,4) DEFAULT '0.0000' COMMENT '% Redução da BC ICMS',
            `SD1_PICMS_STM` decimal(6,2) DEFAULT '0.00' COMMENT '% Aliquota do ICMS-ST Carga M�dia',
            `SD1_BaseImp` decimal(11,2) DEFAULT '0.00' COMMENT 'BC ICMS',
            `SD1_PImp` decimal(5,2) DEFAULT '0.00',
            `SD1_ValImp` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do ICMS',
            `SD1_BaseFCP` double(15,2) DEFAULT '0.00' COMMENT 'Valor da Base de Cálculo do FCP',
            `SD1_PFCP` decimal(7,4) DEFAULT '0.0000' COMMENT 'Percentual do Fundo de Combate à Pobreza (FCP)',
            `SD1_ValFCP` double(15,2) DEFAULT '0.00' COMMENT 'Valor do Fundo de Combate à Pobreza (FCP)',
            `SD1_ValICMSDeson` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do ICMS Desonerado',
            `SD1_MotivoDesonICMS` varchar(2) COLLATE latin1_general_ci DEFAULT '',
            `SD1_ValICMSOp` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do ICMS da Operacao',
            `SD1_PDif` decimal(9,4) DEFAULT '0.0000' COMMENT '% do Diferimento',
            `SD1_ValICMSDif` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do ICMS diferido',
            `SD1_PBaseOpPropria` decimal(5,2) DEFAULT '0.00' COMMENT '% BC da operacao propria',
            `SD1_ModBaseICMS_ST` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Modalid. de determ. da BC ICMS ST',
            `SD1_PRedBaseICMS_ST` decimal(9,4) DEFAULT '0.0000' COMMENT '% Redução da BC ICMS',
            `SD1_PMargemICMS_ST` decimal(5,2) DEFAULT '0.00' COMMENT '% Margem valor adic. ICMS ST',
            `SD1_NaoAjustaMargem` varchar(1) COLLATE latin1_general_ci DEFAULT '0' COMMENT '1=altera margem do ICMS-ST',
            `SD1_BaseICMS_ST` decimal(11,2) DEFAULT '0.00' COMMENT 'Base de calculo ICMS ST',
            `SD1_PICMS_ST` decimal(4,2) DEFAULT '0.00' COMMENT 'Aliquota ICMS ST',
            `SD1_ValICMS_STBruto` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do ICMS ST',
            `SD1_ValICMS_STLiquido` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do ICMS ST',
            `SD1_ValICMSSubstituto` decimal(13,2) DEFAULT '0.00' COMMENT 'Valor do ICMS próprio do Substituto',
            `SD1_BaseFCPST` double(15,2) DEFAULT '0.00' COMMENT 'Valor da Base de Cálculo do FCP retido por Substituição Tributária',
            `SD1_PFCPST` decimal(7,4) DEFAULT '0.0000' COMMENT 'Percentual do FCP retido por Substituição Tributária',
            `SD1_ValFCPST` double(15,2) DEFAULT '0.00' COMMENT 'Valor do FCP retido por Substituição Tributária',
            `SD1_IDSAE_ST` int DEFAULT '0' COMMENT 'ID da UF do ICMS ST devido na operacao',
            `SD1_BaseUFDestino` decimal(11,2) DEFAULT '0.00' COMMENT 'Valor da Base de Calculo do ICMS na UF do destinatario',
            `SD1_BaseFCPUFDest` double(15,2) DEFAULT '0.00' COMMENT 'Valor da BC FCP na UF de destino',
            `SD1_PICMSFCP` decimal(4,2) DEFAULT '0.00' COMMENT 'Percentual do ICMS relativo ao Fundo de Combate a Pobreza (FCP) na UF de destino',
            `SD1_PICMSUFDestino` decimal(4,2) DEFAULT '0.00' COMMENT 'Aliquota interna da UF de destino',
            `SD1_PICMSInterest` decimal(4,2) DEFAULT '0.00' COMMENT 'Aliquota interestadual das UF envolvidas',
            `SD1_PICMSInterestPart` decimal(5,2) DEFAULT '0.00' COMMENT 'Percentual provisorio de partilha do ICMS Interestadual',
            `SD1_ValFCPUFDest` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do ICMS relativo ao Fundo de Combate a Pobreza (FCP) da UF de destino',
            `SD1_ValICMSUFDest` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do ICMS Interestadual para a UF de destino',
            `SD1_ValICMSUFRemet` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do ICMS Interestadual para a UF do remetente',
            `SD1_CEST` varchar(7) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Codigo Especificador da Substituicao Tributaria – CEST',
            `SD1_IndEscala` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Indicador de Escala Relevante (S/N)',
            `SD1_CNPJFab` varchar(14) COLLATE latin1_general_ci DEFAULT '' COMMENT 'CNPJ do Fabricante da Mercadoria',
            `SD1_CodBenef` varchar(10) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Código de Benefício Fiscal na UF aplicado ao item',
            `SD1_SitTribIPI` varchar(2) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Sit. Tribut. IPI',
            `SD1_ClsEnquadrIPI` varchar(5) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Classe de enquadramento IPI',
            `SD1_CodEnquadrIPI` varchar(3) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Codigo de enquadramento IPI',
            `SD1_CNPJProdIPI` varchar(14) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'CNPJ do produtor',
            `SD1_CodSeloIPI` varchar(60) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Codigo do selo de controle',
            `SD1_QuantSeloIPI` decimal(12,0) DEFAULT '0' COMMENT 'Quant. do selo de controle',
            `SD1_TipoCalcIPI` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Tipo de calculo IPI: P ou V',
            `SD1_BaseIPI` decimal(11,2) DEFAULT '0.00' COMMENT 'Base de calculo IPI',
            `SD1_PIPI` decimal(4,2) DEFAULT '0.00' COMMENT 'Aliquota IPI',
            `SD1_QuantUnidIPI` decimal(10,4) DEFAULT '0.0000' COMMENT 'Quant. total na unidade padrao',
            `SD1_ValUnidIPI` decimal(10,4) DEFAULT '0.0000' COMMENT 'Valor por unidade',
            `SD1_ValIPI` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor IPI',
            `SD1_PDevol` decimal(5,2) DEFAULT '0.00' COMMENT 'Percentual da mercadoria devolvida',
            `SD1_ValIPIDevol` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do IPI devolvido',
            `SD1_SitTribPIS` varchar(2) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Sit. Tribut. PIS',
            `SD1_TipoCalcPIS` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Tipo de calculo PIS: P ou V',
            `SD1_BasePIS` decimal(11,2) DEFAULT '0.00' COMMENT 'Base de calculo PIS',
            `SD1_PPIS` decimal(4,2) DEFAULT '0.00' COMMENT 'Aliquota PIS em %',
            `SD1_ValUnidPIS` decimal(10,4) DEFAULT '0.0000' COMMENT 'Aliquota PIS em reais',
            `SD1_QuantUnidPIS` decimal(10,4) DEFAULT '0.0000' COMMENT 'Quant. vendida PIS',
            `SD1_ValPIS` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do PIS',
            `SD1_ValPIS_CREDITO` decimal(10,2) DEFAULT NULL COMMENT 'Valor calculado para o PIS com base na Aliquota obtida nos parametros da NCM para efeito de crédito quando empresa do Lucro real. Usado atualmente para compor o cálculo do custo medio.',
            `SD1_PPIS_CREDITO` decimal(5,2) DEFAULT NULL COMMENT 'Aliquota obtida nos parametros da NCM para efeito de crédito quando empresa do Lucro real. Usado atualmente para compor o cálculo do custo medio.',
            `SD1_TipoCalcPIS_ST` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Tipo de calculo PIS ST: P ou V',
            `SD1_BasePIS_ST` decimal(11,2) DEFAULT '0.00' COMMENT 'Base de calculo PIS ST',
            `SD1_PPIS_ST` decimal(4,2) DEFAULT '0.00' COMMENT 'Aliquota PIS ST em %',
            `SD1_ValUnidPIS_ST` decimal(10,4) DEFAULT '0.0000' COMMENT 'Aliquota PIS ST em reais',
            `SD1_QuantUnidPIS_ST` decimal(10,4) DEFAULT '0.0000' COMMENT 'Quant. vendida PIS ST',
            `SD1_SitTribCOFINS` varchar(2) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Sit. Tribut. COFINS',
            `SD1_TipoCalcCOFINS` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Tipo de calculo COFINS: P ou V',
            `SD1_BaseCOFINS` decimal(11,2) DEFAULT '0.00' COMMENT 'Base de calculo COFINS',
            `SD1_PCOFINS` decimal(4,2) DEFAULT '0.00' COMMENT 'Aliquota COFINS em %',
            `SD1_ValUnidCOFINS` decimal(10,4) DEFAULT '0.0000' COMMENT 'Aliquota COFINS em reais',
            `SD1_QuantUnidCOFINS` decimal(10,4) DEFAULT '0.0000' COMMENT 'Quant. vendida COFINS',
            `SD1_ValCOFINS` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do COFINS',
            `SD1_ValCOFINS_CREDITO` decimal(10,2) DEFAULT NULL COMMENT 'Valor calculado para o COFINS com base na Aliquota obtida nos parametros da NCM para efeito de crédito quando empresa do Lucro real. Usado atualmente para compor o cálculo do custo medio.',
            `SD1_PCOFINS_CREDITO` decimal(5,2) DEFAULT NULL COMMENT 'Aliquota obtida nos parametros da NCM para efeito de crédito quando empresa do Lucro real. Usado atualmente para compor o cálculo do custo medio.',
            `SD1_TipoCalcCOFINS_ST` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Tipo de calculo COFINS ST: P ou V',
            `SD1_BaseCOFINS_ST` decimal(11,2) DEFAULT '0.00' COMMENT 'Base de calculo COFINS ST',
            `SD1_PCOFINS_ST` decimal(4,2) DEFAULT '0.00' COMMENT 'Aliquota COFINS ST em %',
            `SD1_ValUnidCOFINS_ST` decimal(10,4) DEFAULT '0.0000' COMMENT 'Aliquota COFINS ST em reais',
            `SD1_QuantUnidCOFINS_ST` decimal(10,4) DEFAULT '0.0000' COMMENT 'Quant. vendida COFINS ST',
            `SD1_ValCOFINS_ST` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do COFINS ST',
            `SD1_ValPIS_ST` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do PIS ST',
            `SD1_TribISSQN` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Tributação ISSQN: N-Normal, R-Retida, S-Substituta, I-Isenta',
            `SD1_BaseISSQN` decimal(11,2) DEFAULT '0.00' COMMENT 'Base de calculo ISSQN',
            `SD1_PISSQN` decimal(8,4) DEFAULT '0.0000' COMMENT '% ISS',
            `SD1_IDSVC` int DEFAULT NULL COMMENT 'ID do Servico QN',
            `SD1_CodServQN` varchar(4) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Codigo do Serviço QN',
            `SD1_IDSAEQN` int DEFAULT '0' COMMENT 'ID do Estado de ocorrencia',
            `SD1_IDSAMQN` int DEFAULT '0' COMMENT 'ID do Municipio de ocorrencia',
            `SD1_ValISSQN` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do ISSQN',
            `SD1_PRedBaseINSS` decimal(4,2) DEFAULT '0.00' COMMENT '% Redução BC INSS',
            `SD1_BaseINSS` decimal(11,2) DEFAULT '0.00' COMMENT 'Base de Calculo INSS',
            `SD1_PINSS` decimal(10,2) NOT NULL DEFAULT '0.00',
            `SD1_ValINSS` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do INSS',
            `SD1_TextoINSS` text COLLATE latin1_general_ci COMMENT 'Texto livre para o INSS',
            `SD1_PAproxFed` decimal(5,2) DEFAULT '0.00' COMMENT '% Aproximado de tributos federais',
            `SD1_PAproxEst` decimal(5,2) DEFAULT '0.00' COMMENT '% Aproximado de tributos estaduais',
            `SD1_PAproxMun` decimal(5,2) DEFAULT '0.00' COMMENT '% Aproximado de tributos municipais',
            `SD1_ValAproxFed` decimal(11,2) DEFAULT '0.00' COMMENT 'Valor aproximado de tributos federais',
            `SD1_ValAproxEst` decimal(11,2) DEFAULT '0.00' COMMENT 'Valor aproximado de tributos estaduais',
            `SD1_ValAproxMun` decimal(11,2) DEFAULT '0.00' COMMENT 'Valor aproximado de tributos municipais',
            `SD1_BaseII` decimal(11,2) DEFAULT '0.00' COMMENT 'BC do Imposto de Importacao',
            `SD1_ValDespesaAduanII` decimal(10,2) DEFAULT '0.00' COMMENT 'Despesas Aduaneiras',
            `SD1_ValIOFII` decimal(10,2) DEFAULT '0.00' COMMENT 'IOF',
            `SD1_PII` decimal(4,2) DEFAULT '0.00' COMMENT '% Imposto de Importacao',
            `SD1_ValII` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do Imposto de Importacao',
            `SD1_ValDespesaII` decimal(10,2) DEFAULT '0.00' COMMENT 'Despesas de Importacao',
            `SD1_TaxaSISCOMEX` decimal(10,2) DEFAULT '0.00' COMMENT 'Taxa SISCOMEX',
            `SD1_SemTitulo` varchar(1) COLLATE latin1_general_ci DEFAULT 'N' COMMENT 'S-nao tem titulo (ValItem=0), N-tem titulo',
            `SD1_AtuEst` varchar(1) COLLATE latin1_general_ci DEFAULT '1' COMMENT 'Atualizacao do estoque: 0-Nao atualizado, 1-Atualizado, 2-Nao atualizar estoque',
            `SD1_Flags` double DEFAULT '0' COMMENT '1-Incluir Frete na BC ICMS, 2-Incl Frete BC ICMS_ST, 4-Incl Seguro BC ICMS, 8-Incl Seguro BC ICMS_ST, 16-Incl Desconto BC ICMS, 32-Incl Desconto BC ICMS_ST, 64-Incl Despesa BC ICMS, 128-Incl Despesa BC ICMS_ST, 256-Incl IPI BC ICMS, 512-Incl IPI BC ICMS_S',
            `SD1_IncluirValFreteBaseICMS` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD1_IncluirValFreteBaseICMS_ST` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD1_IncluirValFreteBaseIPI` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD1_IncluirValFreteBasePIS` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD1_IncluirValFreteBaseCOFINS` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD1_IncluirValFreteBaseII` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD1_IncluirValSeguroBaseICMS` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD1_IncluirValSeguroBaseICMS_ST` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD1_IncluirValSeguroBaseIPI` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD1_IncluirValSeguroBasePIS` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD1_IncluirValSeguroBaseCOFINS` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD1_IncluirValSeguroBaseII` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD1_IncluirValDescontoBaseICMS` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD1_IncluirValDescontoBaseICMS_ST` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD1_IncluirValDescontoBaseIPI` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD1_IncluirValDescontoBasePIS` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD1_IncluirValDescontoBaseCOFINS` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD1_IncluirValDescontoBaseII` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD1_IncluirValDespesaBaseICMS` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD1_IncluirValDespesaBaseICMS_ST` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD1_IncluirValDespesaBaseIPI` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD1_IncluirValDespesaBasePIS` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD1_IncluirValDespesaBaseCOFINS` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD1_IncluirValDespesaBaseII` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD1_IncluirValIPIBaseICMS` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD1_IncluirValIPIBaseICMS_ST` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD1_DtValidade` datetime  COMMENT 'Data de Validade',
            `SD1_Aprova` varchar(1) COLLATE latin1_general_ci DEFAULT NULL,
            `SD1_ExigibilidadeISSQN` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Indicador da exigibilidade do ISS',
            `SD1_IncentivoFiscalISSQN` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Indicador de incentivo Fiscal',
            `SD1_InfAdProd` text COLLATE latin1_general_ci COMMENT 'Informacoes adicionais do produto',
            `SD1_NumProcessoSuspISSQN` varchar(30) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Número do processo judicial ou administrativo de suspensão da exigibilidade',
            `SD1_CodProdANVISA` varchar(13) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Código de Produto da ANVISA',
            `SD1_MotivoIsencaoANVISA` varchar(255) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Motivo da isenção da ANVISA',
            `SD1_DescANP` varchar(95) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Descrição do produto conforme ANP',
            `SD1_PGLP` decimal(7,4) DEFAULT '0.0000' COMMENT 'Percentual do GLP derivado do petróleo no produto GLP (cProdANP=210203001)',
            `SD1_PGNn` decimal(7,4) DEFAULT '0.0000' COMMENT 'Percentual de Gás Natural Nacional - GLGNn para o produto GLP (cProdANP=210203001)',
            `SD1_PGNi` decimal(7,4) DEFAULT '0.0000' COMMENT 'Percentual de Gás Natural Importado - GLGNi para o produto GLP (cProdANP=210203001)',
            `SD1_ValPart` double(15,2) DEFAULT '0.00' COMMENT 'Valor de partida (cProdANP=210203001)',
            `SD1_CRIADOR` int unsigned NOT NULL COMMENT 'Id do Criador',
            `SD1_ALTERADOR` int unsigned NOT NULL DEFAULT '0' COMMENT 'Id do último alterador',
            `SD1_DT_INC` datetime NOT NULL COMMENT 'Data de inclusão do registro',
            `SD1_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteração do registro',
            `SD1_STATUS` int NOT NULL DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
            `SD1_ProdEspec` varchar(9) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Tipo de produto especifico',
            `SD1_tpOp` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Tipo da operação de venda do veiculo',
            `SD1_Chassi` varchar(17) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Chassi do veiculo',
            `SD1_cCor` varchar(4) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Cor',
            `SD1_xCor` varchar(40) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Descricao da Cor',
            `SD1_Pot` varchar(4) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Potencia Motor (CV)',
            `SD1_Cilin` varchar(4) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Cilindradas',
            `SD1_PesoL` varchar(9) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Peso Liquido',
            `SD1_PesoB` varchar(9) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Peso Bruto',
            `SD1_nSerieVeic` varchar(9) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Serial (serie)',
            `SD1_tpComb` varchar(2) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Tipo de combustivel RENAVAM',
            `SD1_nMotor` varchar(21) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Numero do motor',
            `SD1_CMT` varchar(9) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Capacidade maxima de tracao',
            `SD1_Dist` varchar(4) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Distancia entre os eixos',
            `SD1_AnoMod` varchar(4) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Ano modelo de fabricacao',
            `SD1_AnoFab` varchar(4) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Ano de fabricacao',
            `SD1_tpPint` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Tipo de pintura',
            `SD1_tpVeic` varchar(2) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Tipo de veiculo RENAVAM',
            `SD1_EspVeic` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Especie de veiculo RENAVAM',
            `SD1_VIN` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Condicao do VIN',
            `SD1_CondVeic` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Condicao do veiculo',
            `SD1_cMod` varchar(6) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Codigo marca modelo RENAVAM',
            `SD1_cCorDENATRAN` varchar(2) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Codigo da cor DENATRAN',
            `SD1_Lota` varchar(3) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Capacidade maxima de lotacao',
            `SD1_tpRest` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Restricao',
            `SD1_nLote` varchar(20) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Num do lote de medicamentos',
            `SD1_qLote` varchar(11) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Qtde de produtos no lote',
            `SD1_dFab` datetime DEFAULT NULL COMMENT 'Data de fabricacao',
            `SD1_dVal` datetime DEFAULT NULL COMMENT 'Data de validade',
            `SD1_vPMC` varchar(15) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Preco maximo consumidor',
            `SD1_tpArma` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Indicador do tipo de arma de fogo',
            `SD1_nSerieArma` varchar(9) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Numero de serie da arma',
            `SD1_nCano` varchar(9) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Numero de serie do cano',
            `SD1_descr` varchar(256) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Descricao completa da arma',
            `SD1_CodANP` varchar(21) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Codigo ANP',
            `SD1_CODIF` varchar(21) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'CODIF - qdo a UF utilizar',
            `SD1_Temp` decimal(16,4) DEFAULT NULL COMMENT 'Qtde combust. faturada a temp. ambiente',
            `SD1_IDSAE_UFCons` varchar(10) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'UF de consumo',
            `SD1_BCProd` decimal(11,2) DEFAULT NULL COMMENT 'Base de calculo da CIDE',
            `SD1_AliqProd` varchar(9) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Aliquota da CIDE',
            `SD1_CIDE` decimal(10,2) DEFAULT NULL COMMENT 'Valor da CIDE',
            `SD1_MOPPNumONU` varchar(4) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Numero da ONU',
            `SD1_MOPPNumRisco` varchar(4) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'número do risco',
            `SD1_MOPPClasse` varchar(4) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Classe do risco',
            `SD1_MOPPSubClasse` varchar(4) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Sub-classe do risco',
            `SD1_MOPPGrpEmbalagem` varchar(4) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Grupo da embalagem',
            `SD1_MOPPClassificado` varchar(10) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Classificado',
            `SD1_MOPPNomeApropriado` varchar(256) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Nome apropriado',
            `SD1_debitoDiferente` varchar(256) COLLATE latin1_general_ci DEFAULT NULL,
            `SD1_creditoDiferente` varchar(256) COLLATE latin1_general_ci DEFAULT NULL,
            `SD1_histDiferente` varchar(256) COLLATE latin1_general_ci DEFAULT NULL,
            `SD1_CreditoICMSPresumido` decimal(10,2) DEFAULT NULL COMMENT 'Valor Crédito Presumido  Base Legal: Art. 11, caput do Anexo III do RICMS/2000-SP e; Decreto nº 51.624/2007.\nConsumido apenas na apuração e livros fiscais do ICMS.',
            `SD1_PCreditoICMSPresumido` decimal(4,2) DEFAULT NULL COMMENT 'Aliquota Crédito Presumido  Base Legal: Art. 11, caput do Anexo III do RICMS/2000-SP e; Decreto nº 51.624/2007. \nConsumido apenas na apuração e livros fiscais do ICMS. Obtido do NCM por estado.',
            `SD1_QBcMono` decimal(15,4) DEFAULT NULL,
            `SD1_AdRemIcms` decimal(6,2) DEFAULT NULL,
            `SD1_VIcmsMono` decimal(15,2) DEFAULT NULL,
            `SD1_QBcMonoReten` decimal(15,4) DEFAULT NULL,
            `SD1_AdRemIcmsReten` decimal(7,4) DEFAULT NULL,
            `SD1_VIcmsMonoReten` decimal(15,2) DEFAULT NULL,
            `SD1_PRedAdRem` decimal(5,2) DEFAULT NULL,
            `SD1_MotRedAdRem` decimal(1,0) DEFAULT NULL,
            `SD1_VIcmsMonoOp` decimal(15,2) DEFAULT NULL,
            `SD1_VIcmsMonoDif` decimal(15,2) DEFAULT NULL,
            `SD1_QBcMonoRet` decimal(15,4) DEFAULT NULL,
            `SD1_AdRemIcmsRet` decimal(6,4) DEFAULT NULL,
            `SD1_VIcmsMonoRet` decimal(15,2) DEFAULT NULL,
            PRIMARY KEY (`SD1_ID`),
            KEY `SD1_EA1` (`SD1_IDEA1`),
            KEY `SD1_SF1` (`SD1_IDSF1`),
            KEY `FK_SD1_SB1` (`SD1_IDSB1`),
            KEY `FK_SD1_SB2` (`SD1_IDSB2`),
            KEY `FK_SD1_SED` (`SD1_IDSED`),
            KEY `FK_SD1_SC2` (`SD1_IDSC2`),
            KEY `FK_SD1_SC4` (`SD1_IDSC4`),
            KEY `FK_SD1_CFO` (`SD1_IDCFO`),
            KEY `FK_SD1_SBW` (`SD1_IDSBW`),
            KEY `IDSF1GUIA` (`SD1_IDSF1GUIA`),
            KEY `FK_SD1_SVC` (`SD1_IDSVC`),
            KEY `IDEA1_TipoMovto` (`SD1_IDEA1`,`SD1_TipoMovto`)
          )
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('SD1');
    }
};
