<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
            CREATE TABLE `spx38940043` (
                `id` int NOT NULL AUTO_INCREMENT,
                `link` int DEFAULT NULL,
                `cpo001` int DEFAULT NULL,
                `cpo002` double DEFAULT NULL,
                `cpo003` int DEFAULT NULL,
                `cpo004` int DEFAULT NULL,
                `cpo005` int DEFAULT NULL,
                `cpo006` int DEFAULT NULL,
                `cpo007` int DEFAULT NULL,
                PRIMARY KEY (`id`),
                KEY `link` (`link`)
            )
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('spx38940043');
    }
};
