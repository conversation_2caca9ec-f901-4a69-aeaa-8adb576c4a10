<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
CREATE TABLE `CRM_OP` (
  `CRM_OP_ID` int NOT NULL AUTO_INCREMENT,
  `CRM_OP_IDEA1` int NOT NULL,
  `CRM_OP_DESC` varchar(255) COLLATE latin1_general_ci NOT NULL,
  `CRM_OP_STEP` char(36) COLLATE latin1_general_ci NOT NULL,
  `CRM_OP_FUNNEL` int NOT NULL,
  `CRM_OP_VALOR` double DEFAULT '0',
  `CRM_OP_MRR` double DEFAULT '0',
  `CRM_OP_IDEMP` int DEFAULT NULL,
  `CRM_OP_STATUS` int NOT NULL DEFAULT '0',
  `CRM_OP_OWNER` int NOT NULL,
  `CRM_OP_IDORIGEM` int DEFAULT NULL,
  `CRM_OP_IDSC5` int DEFAULT NULL,
  `CRM_OP_DT_INC` datetime DEFAULT CURRENT_TIMESTAMP,
  `CRM_OP_DT_CLOSE` datetime DEFAULT NULL,
  `CRM_OP_UUID` varchar(36) COLLATE latin1_general_ci NOT NULL,
  `CRM_OP_ENV` text COLLATE latin1_general_ci,
  `CRM_OP_SOURCE` int DEFAULT NULL,
  PRIMARY KEY (`CRM_OP_ID`),
  KEY `CRM_OP_IDEA1` (`CRM_OP_IDEA1`,`CRM_OP_FUNNEL`,`CRM_OP_STEP`),
  KEY `CRM_OP_IDORIGEM` (`CRM_OP_IDORIGEM`),
  KEY `CRM_OP_UUID` (`CRM_OP_UUID`),
  KEY `CRM_OP_SOURCE` (`CRM_OP_SOURCE`),
  KEY `CRM_OP_IDEA1_2` (`CRM_OP_IDEA1`,`CRM_OP_IDEMP`)
);
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('CRM_OP');
    }
};
