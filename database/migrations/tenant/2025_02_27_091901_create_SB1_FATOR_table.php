<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
        CREATE TABLE `SB1_FATOR` (
            `SB1_FATOR_ID` int NOT NULL AUTO_INCREMENT,
            `SB1_FATOR_IDEA1` int NOT NULL,
            `SB1_FATOR_IDSB1` int NOT NULL,
            `SB1_FATOR_IDSB1_UMComercial` int NOT NULL,
            `SB1_FATOR_FC` decimal(13,8) DEFAULT '1.00000000',
            `SB1_FATOR_PadraoC` varchar(1) COLLATE latin1_general_ci DEFAULT 'N',
            `SB1_FATOR_PadraoV` varchar(1) COLLATE latin1_general_ci DEFAULT 'N',
            `SB1_FATOR_PadraoCT` varchar(1) COLLATE latin1_general_ci DEFAULT 'N',
            `SB1_FATOR_PadraoVT` varchar(1) COLLATE latin1_general_ci DEFAULT 'N',
            `SB1_FATOR_CRIADOR` int unsigned NOT NULL,
            `SB1_FATOR_ALTERADOR` int unsigned NOT NULL DEFAULT '0',
            `SB1_FATOR_DT_INC` datetime NOT NULL,
            `SB1_FATOR_DT_ALT` datetime DEFAULT NULL,
            `SB1_FATOR_STATUS` int NOT NULL DEFAULT '0',
            PRIMARY KEY (`SB1_FATOR_ID`),
            KEY `SB1_FATOR_Chave` (`SB1_FATOR_IDEA1`,`SB1_FATOR_IDSB1_UMComercial`),
            KEY `FK_SB1_FATOR_UMComercial` (`SB1_FATOR_IDSB1_UMComercial`)
        )
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('SB1_FATOR');
    }
};
