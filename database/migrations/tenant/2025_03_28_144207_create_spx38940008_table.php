<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
            CREATE TABLE `spx38940008` (
              `id` int NOT NULL AUTO_INCREMENT,
              `link` int DEFAULT NULL,
              `cpo001` int DEFAULT NULL,
              `cpo003` int DEFAULT NULL,
              `cpo004` int DEFAULT NULL,
              `cpo005` date DEFAULT NULL,
              `cpo006` varchar(5) COLLATE latin1_general_ci DEFAULT NULL,
              `cpo007` varchar(5) COLLATE latin1_general_ci DEFAULT NULL,
              `cpo008` longtext COLLATE latin1_general_ci,
              PRIMARY KEY (`id`),
              KEY `link` (`link`),
              KEY `cpo001` (`cpo001`)
            )
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('spx38940008');
    }
};
