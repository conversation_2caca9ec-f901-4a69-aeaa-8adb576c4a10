<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('integration_cutoffs', function (Blueprint $table) {
            $table->dateTime('last_SB1_UM_update')->nullable()->after('last_SEP_update');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('integration_cutoffs', function (Blueprint $table) {
            $table->dropColumn('last_SB1_UM_update');
        });
    }
};
