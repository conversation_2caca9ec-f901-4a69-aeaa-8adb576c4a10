<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
CREATE TABLE `SC3` (
  `SC3_ID` int NOT NULL AUTO_INCREMENT,
  `SC3_IDEA1` int NOT NULL COMMENT 'ID da Empresa',
  `SC3_Tipo` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'P-Pedido de Compra, D-Desp. p/ Reembolso',
  `SC3_Emissao` datetime NOT NULL COMMENT ' ',
  `SC3_Doc` varchar(30) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Documento',
  `SC3_NumSeq` int DEFAULT '0' COMMENT 'Numero Sequencial',
  `SC3_IDSA1` int NOT NULL COMMENT 'ID Fornecedor',
  `SC3_IDSA1Visitado` int DEFAULT '0',
  `SC3_CotDoc` varchar(255) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Guarda o Documento da cotação',
  `SC3_Hist` varchar(255) COLLATE latin1_general_ci NOT NULL COMMENT 'Histï¿½rico',
  `SC3_Currency` varchar(4) COLLATE latin1_general_ci DEFAULT '',
  `SC3_Incoterms` varchar(10) COLLATE latin1_general_ci DEFAULT '',
  `SC3_City` varchar(3) COLLATE latin1_general_ci DEFAULT '',
  `SC3_Terms` int DEFAULT '0',
  `SC3_Comission` decimal(4,1) DEFAULT '0.0',
  `SC3_CalculateComission` varchar(15) COLLATE latin1_general_ci DEFAULT '',
  `SC3_IDSA1Manufacturer` int DEFAULT '0',
  `SC3_PortOrigem` varchar(3) COLLATE latin1_general_ci DEFAULT '',
  `SC3_IDSA3Team` int DEFAULT '0',
  `SC3_IDSA3Seller1` int DEFAULT '0',
  `SC3_IDSA3Seller2` int DEFAULT '0',
  `SC3_IDSA3Seller3` int DEFAULT '0',
  `SC3_DtPrevCom` datetime DEFAULT NULL COMMENT 'Data de previsão de entrada da nota fiscal',
  `SC3_PrazoEntrega` int NOT NULL DEFAULT '0',
  `SC3_ValFrete` decimal(10,2) DEFAULT '0.00' COMMENT 'Frete do Pedido  que caso maior que zero, deve ser rateado nos itens (SC4_ValFrete)',
  `SC3_PercDesconto` decimal(4,2) DEFAULT '0.00' COMMENT 'Percentagem de desconto no valor da soma dos itens (SC4_ValItem) ,que caso maior que zero, deve ser atribuido a todos os itens (SC4_PercDesconto)',
  `SC3_ValDesconto` decimal(10,2) DEFAULT '0.00' COMMENT 'Desconto do Pedido, que caso maior que zero, deve ser rateado nos itens (SC4_ValDesconto)',
  `SC3_Campo01` longtext COLLATE latin1_general_ci,
  `SC3_Campo02` longtext COLLATE latin1_general_ci,
  `SC3_Campo03` longtext COLLATE latin1_general_ci,
  `SC3_Campo04` longtext COLLATE latin1_general_ci,
  `SC3_Campo05` longtext COLLATE latin1_general_ci,
  `SC3_Campo06` longtext COLLATE latin1_general_ci,
  `SC3_Campo07` longtext COLLATE latin1_general_ci,
  `SC3_Campo08` longtext COLLATE latin1_general_ci,
  `SC3_Campo09` longtext COLLATE latin1_general_ci,
  `SC3_Campo10` longtext COLLATE latin1_general_ci,
  `SC3_Campo11` longtext COLLATE latin1_general_ci,
  `SC3_Campo12` longtext COLLATE latin1_general_ci,
  `SC3_Campo13` longtext COLLATE latin1_general_ci,
  `SC3_Campo14` longtext COLLATE latin1_general_ci,
  `SC3_Campo15` longtext COLLATE latin1_general_ci,
  `SC3_Campo16` longtext COLLATE latin1_general_ci,
  `SC3_Campo17` longtext COLLATE latin1_general_ci,
  `SC3_Campo18` longtext COLLATE latin1_general_ci,
  `SC3_Campo19` longtext COLLATE latin1_general_ci,
  `SC3_Campo20` longtext COLLATE latin1_general_ci,
  `SC3_Campo21` longtext COLLATE latin1_general_ci,
  `SC3_Campo22` longtext COLLATE latin1_general_ci,
  `SC3_Campo23` longtext COLLATE latin1_general_ci,
  `SC3_Campo24` longtext COLLATE latin1_general_ci,
  `SC3_Campo25` longtext COLLATE latin1_general_ci,
  `SC3_Campo26` longtext COLLATE latin1_general_ci,
  `SC3_Campo27` longtext COLLATE latin1_general_ci,
  `SC3_Campo28` longtext COLLATE latin1_general_ci,
  `SC3_Campo29` longtext COLLATE latin1_general_ci,
  `SC3_Campo30` longtext COLLATE latin1_general_ci,
  `SC3_CRIADOR` int unsigned NOT NULL COMMENT 'ID do Criador',
  `SC3_ALTERADOR` int unsigned NOT NULL DEFAULT '0' COMMENT 'ID do último alterador',
  `SC3_DT_INC` datetime NOT NULL COMMENT 'Data de inclusão do registro',
  `SC3_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteração do registro',
  `SC3_StaPedido` int DEFAULT NULL COMMENT 'Status do Pedido',
  `SC3_STATUS` int NOT NULL DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
  `SC3_ValTotal` decimal(10,8) DEFAULT NULL COMMENT 'Valor total pedido',
  PRIMARY KEY (`SC3_ID`),
  KEY `FK_SC3_SA1` (`SC3_IDSA1`),
  KEY `FK_SC3_SA1Visitado` (`SC3_IDSA1Visitado`),
  KEY `SC3_IDEA1` (`SC3_IDEA1`)
);
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('SC3');
    }
};
