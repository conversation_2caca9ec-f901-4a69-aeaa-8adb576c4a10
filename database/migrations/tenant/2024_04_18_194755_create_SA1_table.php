<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
CREATE TABLE `SA1` (
  `SA1_ID` int NOT NULL AUTO_INCREMENT,
  `SA1_IDEA1` int NOT NULL COMMENT 'Id da Empresa',
  `SA1_IDSAP` int DEFAULT '0' COMMENT 'ID do País',
  `SA1_IDSAE` int DEFAULT '0' COMMENT 'ID do Estado',
  `SA1_IDSAM` int DEFAULT '0' COMMENT 'ID do Municipio',
  `SA1_IDSAPEnt` int DEFAULT '0' COMMENT 'ID do País de Entrega',
  `SA1_IDSAEEnt` int DEFAULT '0' COMMENT 'ID do Estado',
  `SA1_IDSAMEnt` int DEFAULT '0' COMMENT 'ID do Municipio',
  `SA1_IDSAECob` int DEFAULT '0' COMMENT 'ID do Estado',
  `SA1_IDSAMCob` int DEFAULT '0' COMMENT 'ID do Municipio',
  `SA1_IDSA3` int DEFAULT '0' COMMENT 'ID do Vendedor',
  `SA1_IDSA4` int DEFAULT '0' COMMENT 'ID da Transportadora',
  `SA1_IDSA4Redespacho` int DEFAULT NULL COMMENT 'Id da segunda Transportadora',
  `SA1_IDSPD` int DEFAULT '0' COMMENT 'ID da conta SPED',
  `SA1_IDSPDFO` int DEFAULT '0' COMMENT 'ID da conta SPED Fornecedor',
  `SA1_IDBL1Cli` int DEFAULT '0' COMMENT 'ID da Linha do Balanco (Cliente)',
  `SA1_IDBL1For` int DEFAULT '0' COMMENT 'ID da Linha do Balanco (Fornecedor)',
  `SA1_IDSEP` int DEFAULT '0' COMMENT 'ID da Forma de Pagamento',
  `SA1_IDSCT` int DEFAULT NULL COMMENT 'ID da Carteira de Produto no Cliente',
  `SA1_Regime` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Regime de Tributacao: 0-Pessoa Fisica, 1-Simples Nacional, 2-Simples Nacional-excesso de sublimite de receita bruta, 3-Lucro Presumido, 4-Lucro Real',
  `SA1_Desc` varchar(60) COLLATE latin1_general_ci NOT NULL COMMENT 'Descrição',
  `SA1_Fantasia` varchar(40) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Nome de fantasia',
  `SA1_End` varchar(70) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Endereço',
  `SA1_Numero` varchar(10) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Numero',
  `SA1_Complemento` varchar(50) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Complemento',
  `SA1_DDI` varchar(3) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'DDI',
  `SA1_DDDCom` varchar(4) COLLATE latin1_general_ci DEFAULT '' COMMENT 'DDD',
  `SA1_TelCom` varchar(9) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Tel',
  `SA1_RamalCom` varchar(5) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Tel',
  `SA1_DDDRes` varchar(4) COLLATE latin1_general_ci DEFAULT '' COMMENT 'DDD',
  `SA1_TelRes` varchar(9) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Tel',
  `SA1_DDDCel` varchar(4) COLLATE latin1_general_ci DEFAULT '' COMMENT 'DDD',
  `SA1_TelCel` varchar(9) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Telefone Celular',
  `SA1_DDDFax` varchar(4) COLLATE latin1_general_ci DEFAULT '' COMMENT 'DDD',
  `SA1_TelFax` varchar(9) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Fax',
  `SA1_DDDEnt` varchar(4) COLLATE latin1_general_ci DEFAULT '' COMMENT 'DDD Entrega',
  `SA1_TelEnt` varchar(8) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Telefone Entrega',
  `SA1_Contato` varchar(60) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Contato',
  `SA1_EMail` varchar(255) COLLATE latin1_general_ci DEFAULT NULL,
  `SA1_EMailLOJA` varchar(255) COLLATE latin1_general_ci DEFAULT NULL,
  `SA1_EMailDANFE` varchar(255) COLLATE latin1_general_ci DEFAULT NULL,
  `SA1_EMailCobranca` varchar(255) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Email de cobranca',
  `SA1_EMailEnt` varchar(60) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Email de entrega',
  `SA1_Senha` varchar(16) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Senha',
  `SA1_Tipo` int DEFAULT '1' COMMENT 'Tipo: 1-Cliente, 2-Fornecedor, 3-Ambos',
  `SA1_Bairro` varchar(30) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Bairro',
  `SA1_Mun` varchar(30) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Municipio',
  `SA1_Est` varchar(2) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Estado',
  `SA1_CEP` varchar(8) COLLATE latin1_general_ci DEFAULT '' COMMENT 'CEP',
  `SA1_CEPInt` varchar(8) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'CEP Internacional',
  `SA1_EndEnt` varchar(70) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Endereço Entrega',
  `SA1_NumeroEnt` varchar(50) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Numero Entrega',
  `SA1_ComplementoEnt` varchar(50) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Complemento Entrega',
  `SA1_BairroEnt` varchar(50) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Bairro Entrega',
  `SA1_MunEnt` varchar(50) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Municipio Entrega',
  `SA1_CEPEnt` varchar(50) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'CEP Entrega',
  `SA1_PaisEnt` varchar(30) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Nome do País de Entrega',
  `SA1_PrazoValidade` int DEFAULT '0' COMMENT 'Prazo de Validade (em dias)',
  `SA1_EndCob` varchar(70) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Endereço Entrega',
  `SA1_NumeroCob` varchar(50) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Numero Entrega',
  `SA1_ComplementoCob` varchar(50) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Complemento Entrega',
  `SA1_BairroCob` varchar(50) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Bairro Entrega',
  `SA1_MunCob` varchar(50) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Municipio Entrega',
  `SA1_CEPCob` varchar(50) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'CEP Entrega',
  `SA1_DtNasc` datetime DEFAULT NULL COMMENT 'Data de Nascimento',
  `SA1_Sexo` varchar(1) COLLATE latin1_general_ci DEFAULT 'M' COMMENT 'Sexo',
  `SA1_CPF` varchar(14) COLLATE latin1_general_ci DEFAULT '' COMMENT 'CGC - CPF',
  `SA1_Inscr` varchar(14) COLLATE latin1_general_ci DEFAULT ' ',
  `SA1_IndicadorIE` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Indicador de IE, nova tag NFE 3.0',
  `SA1_InscrM` varchar(12) COLLATE latin1_general_ci DEFAULT ' ',
  `SA1_InscrEnt` varchar(14) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Inscrição Estadual do Estabelecimento Recebedor (entrega)',
  `SA1_SUFRAMA` varchar(9) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Inscricao na SUFRAMA',
  `SA1_RG` varchar(20) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'RG',
  `SA1_TabelaPreco` varchar(3) COLLATE latin1_general_ci DEFAULT '1',
  `SA1_IDTPRECO` int DEFAULT NULL COMMENT 'ID Nova Tabela de Preço',
  `SA1_Parcelas` int DEFAULT '1' COMMENT 'Forma de pagto.: nr. de parcelas',
  `SA1_Intervalo` int DEFAULT '0' COMMENT 'Forma de pagto.: intervalo em dias entre as parcelas',
  `SA1_IntervaloVariavel` varchar(60) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Forma de pagto.: intervalos variaveis entre as parcelas (ex.: 15,40,20)',
  `SA1_Vencto` int DEFAULT '0' COMMENT 'Forma de pagto.: dia do vencimento da parcela',
  `SA1_ValMinimoParc` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT 'Valor mínimo da parcela',
  `SA1_JurosFinDia` decimal(8,6) NOT NULL DEFAULT '0.000000' COMMENT 'Juros Financeiro ao Dia para paamentos parcelados',
  `SA1_ValMinimoComp` decimal(10,2) DEFAULT NULL COMMENT 'Valor mínimo de compras',
  `SA1_ValLimiteCred` decimal(10,2) DEFAULT NULL COMMENT 'Valor limite de créditos',
  `SA1_DtVenctoCred` datetime DEFAULT NULL COMMENT 'Data de vencimento do crédito',
  `SA1_TipoAssinante` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT 'NF Modelo 22 - Classe de consumo ou Tipo de assinante',
  `SA1_TipoUtilizacao` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT 'NF Modelo 22 - Fase ou Tipo de utilizacao',
  `SA1_GrupoTensao` varchar(2) COLLATE latin1_general_ci DEFAULT '00' COMMENT 'NF Modelo 22 - Grupo de tensao',
  `SA1_NTTP` varchar(12) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Número do terminal telefônico principal',
  `SA1_NTTUC` varchar(12) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Número do terminal telefônico ou da unidade consumidora',
  `SA1_ICMS_STM` varchar(1) COLLATE latin1_general_ci DEFAULT '0' COMMENT 'Calcula ICMS-ST Pela Carga Tributária Média? (0-Não 1-Sim)',
  `SA1_ICMS_STM_ALIQ` decimal(6,2) DEFAULT '0.00' COMMENT 'Aliquota ICMS-ST Carga Tributária Média',
  `SA1_Campo1` longtext COLLATE latin1_general_ci,
  `SA1_Campo2` longtext COLLATE latin1_general_ci,
  `SA1_Campo3` longtext COLLATE latin1_general_ci,
  `SA1_Campo4` longtext COLLATE latin1_general_ci,
  `SA1_Campo5` longtext COLLATE latin1_general_ci,
  `SA1_Campo6` longtext COLLATE latin1_general_ci,
  `SA1_Campo7` longtext COLLATE latin1_general_ci,
  `SA1_Campo8` longtext COLLATE latin1_general_ci,
  `SA1_Campo9` longtext COLLATE latin1_general_ci,
  `SA1_Campo10` longtext COLLATE latin1_general_ci,
  `SA1_Campo11` longtext COLLATE latin1_general_ci,
  `SA1_Campo12` longtext COLLATE latin1_general_ci,
  `SA1_Campo13` longtext COLLATE latin1_general_ci,
  `SA1_Campo14` longtext COLLATE latin1_general_ci,
  `SA1_Campo15` longtext COLLATE latin1_general_ci,
  `SA1_Campo16` longtext COLLATE latin1_general_ci,
  `SA1_Campo17` longtext COLLATE latin1_general_ci,
  `SA1_Campo18` longtext COLLATE latin1_general_ci,
  `SA1_Campo19` longtext COLLATE latin1_general_ci,
  `SA1_Campo20` longtext COLLATE latin1_general_ci,
  `SA1_Campo21` longtext COLLATE latin1_general_ci,
  `SA1_Campo22` longtext COLLATE latin1_general_ci,
  `SA1_Campo23` longtext COLLATE latin1_general_ci,
  `SA1_Campo24` longtext COLLATE latin1_general_ci,
  `SA1_Campo25` longtext COLLATE latin1_general_ci,
  `SA1_Campo26` longtext COLLATE latin1_general_ci,
  `SA1_Campo27` longtext COLLATE latin1_general_ci,
  `SA1_Campo28` longtext COLLATE latin1_general_ci,
  `SA1_Campo29` longtext COLLATE latin1_general_ci,
  `SA1_Campo30` longtext COLLATE latin1_general_ci,
  `SA1_Campo31` longtext COLLATE latin1_general_ci,
  `SA1_Campo32` longtext COLLATE latin1_general_ci,
  `SA1_Campo33` longtext COLLATE latin1_general_ci,
  `SA1_Campo34` longtext COLLATE latin1_general_ci,
  `SA1_Campo35` longtext COLLATE latin1_general_ci,
  `SA1_Campo36` longtext COLLATE latin1_general_ci,
  `SA1_Campo37` longtext COLLATE latin1_general_ci,
  `SA1_Campo38` longtext COLLATE latin1_general_ci,
  `SA1_Campo39` longtext COLLATE latin1_general_ci,
  `SA1_Campo40` longtext COLLATE latin1_general_ci,
  `SA1_Campo41` longtext COLLATE latin1_general_ci,
  `SA1_Campo42` longtext COLLATE latin1_general_ci,
  `SA1_Campo43` longtext COLLATE latin1_general_ci,
  `SA1_Campo44` longtext COLLATE latin1_general_ci,
  `SA1_Campo45` longtext COLLATE latin1_general_ci,
  `SA1_Campo46` longtext COLLATE latin1_general_ci,
  `SA1_Campo47` longtext COLLATE latin1_general_ci,
  `SA1_Campo48` longtext COLLATE latin1_general_ci,
  `SA1_Campo49` longtext COLLATE latin1_general_ci,
  `SA1_Campo50` longtext COLLATE latin1_general_ci,
  `SA1_Obs` text COLLATE latin1_general_ci COMMENT 'Texto livre',
  `SA1_AreasRestritas` varchar(10) CHARACTER SET latin1 COLLATE latin1_german1_ci DEFAULT 'NNNN' COMMENT 'Indica se o cliente tem acesso às diversas áreas restritass, 1 bytes para cada área de maneira posicional.',
  `SA1_SitCadastro` varchar(1) COLLATE latin1_general_ci DEFAULT '1' COMMENT 'Indica a situação dos dados cadastrais : 0-aguardadndo aprovação;1-aprovado;2-inativo',
  `SA1_EnviadoSysLoja` varchar(1) COLLATE latin1_general_ci DEFAULT 'N' COMMENT 'Indica se ja foi enviado ao Sistema SysLoja',
  `SA1_EnviadoZanthus` varchar(1) COLLATE latin1_general_ci DEFAULT 'N' COMMENT 'Indica se ja foi enviado ao Sistema Zanthus',
  `SA1_Banco` varchar(3) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Numero do Banco',
  `SA1_Agencia` varchar(4) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Numero da Agencia',
  `SA1_AgenciaDig` varchar(4) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Digito da Agencia',
  `SA1_Conta` varchar(50) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Numero da Conta Bancaria',
  `SA1_ContaDig` varchar(3) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Digito da Conta Bancaria',
  `SA1_PrazoEntrega` int NOT NULL DEFAULT '0',
  `SA1_ModFretePadrao` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Modalidade do frete Padrão\n\n0=Contratação do Frete por conta do Remetente (CIF);\n1=Contratação do Frete por conta do Destinatário (FOB);\n2=Contratação do Frete por conta de Terceiros;\n3=Transporte Próprio por conta do Remetente;\n4=Transporte Próprio por conta do Destinatário;\n9=Sem Ocorrência de Transporte.',
  `SA1_CRIADOR` int unsigned NOT NULL COMMENT 'Id do Criador',
  `SA1_ALTERADOR` int unsigned NOT NULL DEFAULT '0' COMMENT 'Id do último alterador',
  `SA1_DT_INC` datetime NOT NULL COMMENT 'Data de inclusão do registro',
  `SA1_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteração do registro',
  `SA1_STATUS` int NOT NULL DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
  `SA1_LAT` decimal(11,7) DEFAULT '0.0000000' COMMENT 'Latitude do cliente',
  `SA1_LNG` decimal(11,7) DEFAULT '0.0000000' COMMENT 'Longitude do cliente',
  `SA1_Newsletter` varchar(1) COLLATE latin1_general_ci DEFAULT 'N',
  `SA1_DT_INC_LV` datetime DEFAULT NULL,
  `SA1_DT_ALT_LV` datetime DEFAULT NULL,
  PRIMARY KEY (`SA1_ID`),
  KEY `SA1_DtNasc` (`SA1_IDEA1`,`SA1_DtNasc`),
  KEY `SA1_Tipo` (`SA1_STATUS`,`SA1_Tipo`),
  KEY `FK_SA1_SA3` (`SA1_IDSA3`),
  KEY `FK_SA1_SA4` (`SA1_IDSA4`),
  KEY `FK_SA1_SAE` (`SA1_IDSAE`),
  KEY `FK_SA1_SAECob` (`SA1_IDSAECob`),
  KEY `FK_SA1_SAEEnt` (`SA1_IDSAEEnt`),
  KEY `FK_SA1_SAM` (`SA1_IDSAM`),
  KEY `FK_SA1_SAMCob` (`SA1_IDSAMCob`),
  KEY `FK_SA1_SAMEnt` (`SA1_IDSAMEnt`),
  KEY `FK_SA1_SAP` (`SA1_IDSAP`),
  KEY `IDEA1_Tipo_SitCadastro` (`SA1_IDEA1`,`SA1_Tipo`,`SA1_SitCadastro`),
  KEY `SA1_EMAIL` (`SA1_IDEA1`,`SA1_EMail`),
  KEY `SA1_EmaiLoja` (`SA1_IDEA1`,`SA1_EMailLOJA`),
  KEY `SA1_CPF` (`SA1_IDEA1`,`SA1_CPF`),
  KEY `SA1_Desc` (`SA1_IDEA1`,`SA1_Desc`),
  KEY `FK_SA1_SAPEnt` (`SA1_IDSAPEnt`)
)
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('SA1');
    }
};
