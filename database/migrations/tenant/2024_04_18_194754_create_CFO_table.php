<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
        CREATE TABLE `CFO` (
          `CFO_ID` int NOT NULL AUTO_INCREMENT,
          `CFO_CFOP` varchar(4) COLLATE latin1_general_ci DEFAULT '',
          `CFO_Desc` varchar(60) COLLATE latin1_general_ci DEFAULT NULL,
          `CFO_DescCompleta` varchar(350) COLLATE latin1_general_ci DEFAULT NULL,
          `CFO_CreditaICMS` int DEFAULT '0' COMMENT 'Credita de ICMS na apuração dos impostos',
          `CFO_CreditaIPI` int DEFAULT '0' COMMENT 'Credita de IPI na apuração dos impostos',
          `CFO_CreditaICMS_ST` int DEFAULT '0' COMMENT 'Credita de ICMS_ST na apuração dos impostos',
          `CFO_Devolucao` int DEFAULT '0' COMMENT '0 = NÃ£o 1 = Sim',
          `CFO_ProdutoProprio` varchar(1) COLLATE latin1_general_ci NOT NULL DEFAULT 'S' COMMENT '0 = Não | 1 = Sim',
          `CFO_ALTERADOR` int unsigned NOT NULL DEFAULT '0' COMMENT 'Id do último alterador',
          `CFO_DT_INC` datetime NOT NULL COMMENT 'Data de inclusão do registro',
          `CFO_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteração do registro',
          `CFO_STATUS` int NOT NULL DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
          `CFO_CRIADOR` int unsigned NOT NULL COMMENT 'Id do Criador',
          PRIMARY KEY (`CFO_ID`),
          KEY `CFO_STATUS` (`CFO_STATUS`,`CFO_CFOP`),
          KEY `CFO_CFOP` (`CFO_CFOP`)
        );
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('CFO');
    }
};
