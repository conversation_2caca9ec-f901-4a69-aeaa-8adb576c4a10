<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
CREATE TABLE `EA2` (
  `EA2_ID` int NOT NULL AUTO_INCREMENT,
  `EA2_Usuario` varchar(32) COLLATE latin1_general_ci NOT NULL COMMENT 'Usuário',
  `EA2_Senha` varchar(32) COLLATE latin1_general_ci NOT NULL COMMENT 'Senha',
  `EA2_Desc` varchar(32) COLLATE latin1_general_ci NOT NULL COMMENT 'Descrição (Nome)',
  `EA2_Perfil_Number` int NOT NULL DEFAULT '0' COMMENT 'Perfil do Usuário',
  `EA2_Email` varchar(128) COLLATE latin1_general_ci NOT NULL COMMENT 'Email',
  `EA2_DtValidade` datetime DEFAULT NULL COMMENT 'Informar a data de validade de login do usuário e hora limite',
  `EA2_Info` text COLLATE latin1_general_ci COMMENT 'Informacoes do perfil do usuario',
  `EA2_FavTip01` varchar(10) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Tipo',
  `EA2_FavMod01` varchar(100) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Nome do Módulo',
  `EA2_FavTip02` varchar(10) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Tipo',
  `EA2_FavMod02` varchar(100) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Nome do Módulo',
  `EA2_FavTip03` varchar(10) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Tipo',
  `EA2_FavMod03` varchar(100) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Nome do Módulo',
  `EA2_FavTip04` varchar(10) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Tipo',
  `EA2_FavMod04` varchar(100) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Nome do Módulo',
  `EA2_FavTip05` varchar(10) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Tipo',
  `EA2_FavMod05` varchar(100) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Nome do Módulo',
  `EA2_FavTip06` varchar(10) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Tipo',
  `EA2_FavMod06` varchar(100) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Nome do Módulo',
  `EA2_FavTip07` varchar(10) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Tipo',
  `EA2_FavMod07` varchar(100) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Nome do Módulo',
  `EA2_FavTip08` varchar(10) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Tipo',
  `EA2_FavMod08` varchar(100) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Nome do Módulo',
  `EA2_FavTip09` varchar(10) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Tipo',
  `EA2_FavMod09` varchar(100) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Nome do Módulo',
  `EA2_FavTip10` varchar(10) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Tipo',
  `EA2_FavMod10` varchar(100) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Nome do Módulo',
  `EA2_Estilo` int NOT NULL DEFAULT '0' COMMENT '0 - Padrão, 1 - Soft, 2 - Hard, 3 - Dark e 4 - Padrao e 5 - Clean',
  `EA2_Ativo` int NOT NULL DEFAULT '1' COMMENT 'Se o usuario tem acesso ao sistema = 1 se nao tem = 0',
  `EA2_DT_ULTLOGON` datetime DEFAULT NULL COMMENT 'Data de último login no sistema',
  `EA2_CRIADOR` int unsigned NOT NULL COMMENT 'Id do Criador',
  `EA2_ALTERADOR` int unsigned NOT NULL DEFAULT '0' COMMENT 'Id do último alterador',
  `EA2_DT_INC` datetime NOT NULL COMMENT 'Data de inclusão do registro',
  `EA2_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteração do registro',
  `EA2_STATUS` int NOT NULL DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
  `EA2_TROCAR_SENHA_PROX_LOGIN` varchar(1) COLLATE latin1_general_ci DEFAULT 'N',
  PRIMARY KEY (`EA2_ID`)
);
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('EA2');
    }
};
