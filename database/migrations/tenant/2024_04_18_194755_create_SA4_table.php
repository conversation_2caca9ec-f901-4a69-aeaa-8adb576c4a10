<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
        CREATE TABLE `SA4` (
          `SA4_ID` int NOT NULL AUTO_INCREMENT,
          `SA4_IDEA1` int NOT NULL COMMENT 'Id da Empresa',
          `SA4_Desc` varchar(40) COLLATE latin1_general_ci NOT NULL COMMENT 'Descrição',
          `SA4_Fantasia` varchar(40) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Nome Fantasia',
          `SA4_End` varchar(50) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Endereço',
          `SA4_Numero` varchar(10) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Numero',
          `SA4_Complemento` varchar(50) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Complemento',
          `SA4_DDD` varchar(2) COLLATE latin1_general_ci DEFAULT '' COMMENT 'DDD',
          `SA4_Tel` varchar(9) COLLATE latin1_general_ci DEFAULT NULL,
          `SA4_Ramal` varchar(5) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Tel',
          `SA4_DDDFax` varchar(2) COLLATE latin1_general_ci DEFAULT '' COMMENT 'DDD',
          `SA4_TelFax` varchar(9) COLLATE latin1_general_ci DEFAULT NULL,
          `SA4_Contato` varchar(30) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Contato',
          `SA4_EMail` varchar(60) COLLATE latin1_general_ci NOT NULL COMMENT 'Email',
          `SA4_Bairro` varchar(30) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Bairro',
          `SA4_Mun` varchar(30) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Municipio',
          `SA4_Est` varchar(2) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Estado',
          `SA4_CEP` varchar(8) COLLATE latin1_general_ci DEFAULT '' COMMENT 'CEP',
          `SA4_CNPJ` varchar(14) COLLATE latin1_general_ci DEFAULT NULL,
          `SA4_Inscr` varchar(14) COLLATE latin1_general_ci NOT NULL DEFAULT '' COMMENT 'Inscricao Estadual',
          `SA4_InscrM` varchar(8) COLLATE latin1_general_ci NOT NULL DEFAULT '' COMMENT 'Inscricao Municipal',
          `SA4_Campo1` text COLLATE latin1_general_ci COMMENT 'Campo livre',
          `SA4_CRIADOR` int unsigned NOT NULL COMMENT 'Id do Criador',
          `SA4_ALTERADOR` int unsigned NOT NULL DEFAULT '0' COMMENT 'Id do último alterador',
          `SA4_DT_INC` datetime NOT NULL COMMENT 'Data de inclusão do registro',
          `SA4_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteração do registro',
          `SA4_STATUS` int NOT NULL DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
          PRIMARY KEY (`SA4_ID`),
          KEY `SA4_Desc` (`SA4_IDEA1`,`SA4_Desc`)
        )
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('SA4');
    }
};
