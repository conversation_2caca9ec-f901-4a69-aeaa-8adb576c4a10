<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
CREATE TABLE `CRM_EMP` (
  `CRM_EMP_ID` int NOT NULL AUTO_INCREMENT,
  `CRM_EMP_IDEA1` int NOT NULL,
  `CRM_EMP_RAZAO` varchar(255) COLLATE latin1_general_ci DEFAULT NULL,
  `CRM_EMP_FANTASIA` varchar(255) COLLATE latin1_general_ci DEFAULT NULL,
  `CRM_EMP_CNPJ` varchar(255) COLLATE latin1_general_ci DEFAULT NULL,
  `CRM_EMP_CEP` varchar(8) COLLATE latin1_general_ci DEFAULT NULL,
  `CRM_EMP_END` varchar(255) COLLATE latin1_general_ci DEFAULT NULL,
  `CRM_EMP_NUM` int DEFAULT NULL,
  `CRM_EMP_COMPL` varchar(255) COLLATE latin1_general_ci DEFAULT NULL,
  `CRM_EMP_BAIRRO` varchar(255) COLLATE latin1_general_ci DEFAULT NULL,
  `CRM_EMP_MUN` int DEFAULT NULL,
  `CRM_EMP_EST` int DEFAULT NULL,
  `CRM_EMP_IDSA1` int DEFAULT NULL,
  `CRM_EMP_DT_INC` datetime DEFAULT CURRENT_TIMESTAMP,
  `CRM_EMP_RG` varchar(255) COLLATE latin1_general_ci DEFAULT NULL,
  PRIMARY KEY (`CRM_EMP_ID`),
  KEY `CRM_EMP_IDEA1` (`CRM_EMP_IDEA1`,`CRM_EMP_CNPJ`),
  KEY `CRM_EMP_IDSA1` (`CRM_EMP_IDSA1`)
);
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('CRM_EMP');
    }
};
