<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
        CREATE TABLE `SG0` (
            `SG0_ID` int NOT NULL AUTO_INCREMENT,
            `SG0_IDEA1` int NOT NULL COMMENT 'ID da Empresa',
            `SG0_IDSB1` int NOT NULL DEFAULT '0' COMMENT 'ID do Produto',
            `SG0_IDSB2` int NOT NULL DEFAULT '0' COMMENT 'ID da Variante do Produto',
            `SG0_IDSBO` int DEFAULT NULL COMMENT 'ID da Operação',
            `SG0_QB` int DEFAULT '0' COMMENT 'Qt. Base da Estrutura',
            `SG0_AutOP` tinyint(1) DEFAULT '0',
            `SG0_Ativo` int NOT NULL DEFAULT '1' COMMENT 'Determina se a estrutura estÃ¡ ativa ou inativa',
            `SG0_CRIADOR` int NOT NULL COMMENT 'ID do Criador',
            `SG0_ALTERADOR` int NOT NULL DEFAULT '0' COMMENT 'ID do Ãºltimo alterador',
            `SG0_DT_INC` datetime NOT NULL COMMENT 'Data de inclusÃ£o do registro',
            `SG0_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteraÃ§Ã£o do registro',
            `SG0_STATUS` int DEFAULT '0',
            PRIMARY KEY (`SG0_ID`),
            UNIQUE KEY `IDEA1_IDSB1_IDSB2` (`SG0_IDEA1`,`SG0_IDSB1`,`SG0_IDSB2`),
            KEY `FK_SG0_SB1` (`SG0_IDSB1`),
            KEY `FK_SG0_SB2` (`SG0_IDSB2`),
            KEY `FK_SG0_SBO` (`SG0_IDSBO`),
            KEY `IDEA1` (`SG0_IDEA1`)
          )
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('SG0');
    }
};
