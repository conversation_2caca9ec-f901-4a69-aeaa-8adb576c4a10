<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
            CREATE TABLE `spx38940001` (
                `id` int NOT NULL AUTO_INCREMENT,
                `link` int DEFAULT NULL,
                `cpo002` int DEFAULT NULL,
                `cpo005` int DEFAULT NULL,
                `cpo006` int DEFAULT NULL,
                `cpo007` date DEFAULT NULL,
                `cpo008` varchar(5) COLLATE latin1_general_ci DEFAULT NULL,
                `cpo009` longtext COLLATE latin1_general_ci,
                `cpo010` date DEFAULT NULL,
                `cpo011` varchar(5) COLLATE latin1_general_ci DEFAULT NULL,
                `cpo012` varchar(50) COLLATE latin1_general_ci DEFAULT NULL,
                `cpo013` varchar(50) COLLATE latin1_general_ci DEFAULT NULL,
                `cpo014` varchar(50) COLLATE latin1_general_ci DEFAULT NULL,
                `cpo015` varchar(50) COLLATE latin1_general_ci DEFAULT NULL,
                `cpo016` varchar(50) COLLATE latin1_general_ci DEFAULT NULL,
                `cpo017` varchar(50) COLLATE latin1_general_ci DEFAULT NULL,
                `cpo018` varchar(50) COLLATE latin1_general_ci DEFAULT NULL,
                `cpo019` varchar(50) COLLATE latin1_general_ci DEFAULT NULL,
                `cpo020` varchar(50) COLLATE latin1_general_ci DEFAULT NULL,
                `cpo021` varchar(15) COLLATE latin1_general_ci DEFAULT NULL,
                `cpo022` varchar(15) COLLATE latin1_general_ci DEFAULT NULL,
                PRIMARY KEY (`id`),
                KEY `link` (`link`)
            )
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('spx38940001');
    }
};
