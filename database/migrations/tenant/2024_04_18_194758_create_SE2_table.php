<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
        CREATE TABLE `SE2` (
            `SE2_ID` int NOT NULL AUTO_INCREMENT,
            `SE2_IDEA1` int NOT NULL COMMENT 'Id da Empresa',
            `SE2_IDSF2` int NOT NULL DEFAULT '0',
            `SE2_IDSE3` int NOT NULL DEFAULT '0',
            `SE2_IDIE2` int NOT NULL DEFAULT '0',
            `SE2_IDDR2` int NOT NULL DEFAULT '0',
            `SE2_IDSE7` int DEFAULT NULL,
            `SE2_Sequencial` int NOT NULL DEFAULT '1' COMMENT 'NÃºmero sequencial das parcelas',
            `SE2_IDRef` int DEFAULT NULL COMMENT 'Id Registro de Origem',
            `SE2_IDSEP` int DEFAULT NULL COMMENT 'ID da Forma de Pagamento',
            `SE2_IDSEA` int DEFAULT NULL COMMENT 'ID da Aglutinacao de Titulos (nos regs. de parcelas da Fatura)',
            `SE2_IDSEARef` int DEFAULT NULL COMMENT 'ID da Aglutinacao de Titulos (nos regs. aglutinados)',
            `SE2_IDSE6` int DEFAULT NULL COMMENT 'ID do Cheque',
            `SE2_IDSA8` int DEFAULT NULL COMMENT 'ID do Cartao de Credito',
            `SE2_Doc` varchar(30) COLLATE latin1_general_ci DEFAULT '',
            `SE2_Hist` varchar(255) COLLATE latin1_general_ci NOT NULL COMMENT 'Histï¿½rico',
            `SE2_CodigoBarras` varchar(60) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Codigo de Barras do boleto',
            `SE2_Vencto` datetime NOT NULL COMMENT 'Vencimento',
            `SE2_Tipo` varchar(13) COLLATE latin1_general_ci NOT NULL COMMENT 'Tipo',
            `SE2_tPag` varchar(2) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Meio de Pagamento: 01=Dinheiro, 02=Cheque, 03=Cartão de Crédito, 04=Cartão de Débito, 05=Crédito Loja, 10=Vale Alimentação, 11=Vale Refeição, 12=Vale Presente, 13=Vale Combustível, 15=Boleto Bancário, 16=Depósito Bancário, 17=Pagamento Instantâneo (PIX), 18=Transferência bancária, Carteira Digital, 19=Programa de fidelidade, Cashback, Crédito Virtual, 90= Sem pagamento, 99=Outros',
            `SE2_ComJuros` varchar(1) COLLATE latin1_general_ci NOT NULL DEFAULT 'N' COMMENT 'Se a parcela tem juros embutido',
            `SE2_Valor` decimal(17,2) NOT NULL COMMENT 'Valor',
            `SE2_ValorPago` decimal(17,2) NOT NULL COMMENT 'Valor pago',
            `SE2_ValorDesconto` decimal(17,2) DEFAULT '0.00' COMMENT 'Valor do desconto',
            `SE2_ValorAcrescimo` decimal(17,2) DEFAULT '0.00' COMMENT 'Valor do acrescimo',
            `SE2_ValorPagoFatura` decimal(17,2) DEFAULT '0.00' COMMENT 'Valor das baixas da Fatura, rateado entre os titulos que a compoem',
            `SE2_ValorDescontoFatura` decimal(17,2) DEFAULT '0.00' COMMENT 'Valor dos decontos da Fatura, rateado entre os titulos que a compoem',
            `SE2_ValorAcrescimoFatura` decimal(17,2) DEFAULT '0.00' COMMENT 'Valor dos acrescimos da Fatura, rateado entre os titulos que a compoem',
            `SE2_Baixa` datetime DEFAULT NULL COMMENT 'Data de baixa',
            `SE2_Credito` datetime DEFAULT NULL COMMENT 'Data do credito na conta corrente',
            `SE2_IDSA6` int DEFAULT NULL COMMENT 'ID Banco',
            `SE2_IDSEFBaixa` int DEFAULT '0' COMMENT 'ID da Fatura baixada por este titulo',
            `SE2_Banco` varchar(3) COLLATE latin1_general_ci DEFAULT NULL,
            `SE2_Agencia` varchar(4) COLLATE latin1_general_ci DEFAULT NULL,
            `SE2_Conta` varchar(50) COLLATE latin1_general_ci DEFAULT NULL,
            `SE2_ContaDig` varchar(3) COLLATE latin1_general_ci DEFAULT NULL,
            `SE2_IDSA7` int NOT NULL COMMENT 'Carteira',
            `SE2_Fatura` int DEFAULT '0' COMMENT 'Nr. da Fatura (SE2_ID de algum dos titulos aglutinados)',
            `SE2_Cheque` varchar(10) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Cheque',
            `SE2_NomeCheque` varchar(150) COLLATE latin1_general_ci DEFAULT NULL,
            `SE2_Reconc` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Reconc(?)',
            `SE2_StatusPagSeguro` varchar(64) COLLATE latin1_general_ci NOT NULL COMMENT 'Status Pag Seguro',
            `SE2_NumBoleto` bigint DEFAULT NULL,
            `SE2_ArquivoRemessa` varchar(255) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Nome do arquivo remessa se foi enviado pro CNAB',
            `SE2_TipoPagRec` int DEFAULT '0' COMMENT '0=Título Normal, 1=Título de Desconto, 2=Título de Acrescimo, 3=Titulo de Desconto por cancelamento',
            `SE2_CRIADOR` int unsigned NOT NULL COMMENT 'Id do Criador',
            `SE2_ALTERADOR` int unsigned NOT NULL DEFAULT '0' COMMENT 'Id do último alterador',
            `SE2_DT_INC` datetime NOT NULL COMMENT 'Data de inclusão do registro',
            `SE2_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteração do registro',
            `SE2_STATUS` int NOT NULL DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
            `SE2_IDTipoDocumento` int DEFAULT NULL COMMENT 'ID Referencia with table TIPODOCUMENTO',
            `SE2_IDSAI` int DEFAULT NULL COMMENT 'ID de ocorrencias Cnab - especifico para pagfor bradesco',
            `SE2_TipoMovimento` int DEFAULT NULL COMMENT 'PagFor - 0=Inclusao 5=Alteracao 9=Exclusao',
            `SE2_FaturaDuplicata` int DEFAULT NULL COMMENT 'PagFor - Numero da Fatura-Duplicata',
            `SE2_CodigoMovimento` varchar(2) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'PagFor',
            `SE2_InstrucaoCheque` varchar(40) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'PagFor - Instrucao cheque OP',
            `SE2_TipoDocCompeTed` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'PagFor - C=Titularidade diferente D=Mesma Titularidade',
            `SE2_TipoContaModalidade` varchar(2) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'PagFor - Tipo de Conta Modalidade DOC/COMPE/TED',
            `SE2_CodigoLancamento` int DEFAULT NULL COMMENT 'PagFor - Codigo de Lancamento',
            `SE2_BoletoEnviadoEmail` int NOT NULL DEFAULT '0' COMMENT '0 - Boleto não enviado por e-mail\n1- Boleto Enviado por e-mail',
            `SE2_TaxaJuros` decimal(9,6) DEFAULT '0.000000',
            `SE2_ValorJuros` decimal(11,2) DEFAULT '0.00',
            `SE2_ValorArrecadado` decimal(17,2) DEFAULT NULL COMMENT 'Valor arrecadado SisPag',
            PRIMARY KEY (`SE2_ID`),
            KEY `SE2_IdOrigem` (`SE2_IDEA1`),
            KEY `FK_SE2_SF2` (`SE2_IDSF2`),
            KEY `FK_SE2_SE3` (`SE2_IDSE3`),
            KEY `FK_SE2_IE2` (`SE2_IDIE2`),
            KEY `FK_SE2_DR2` (`SE2_IDDR2`),
            KEY `FK_SE2_SA6` (`SE2_IDSA6`),
            KEY `SE2_Fatura` (`SE2_Fatura`),
            KEY `FK_SE2_SE2` (`SE2_IDRef`),
            KEY `FK_SE2_SEP` (`SE2_IDSEP`),
            KEY `SE2_IDSEA` (`SE2_IDSEA`),
            KEY `SE2_IDSEARef` (`SE2_IDSEARef`),
            KEY `FK_SE2_SE6` (`SE2_IDSE6`),
            KEY `IDEA1_Vencto` (`SE2_IDEA1`,`SE2_Vencto`),
            KEY `IDSE7` (`SE2_IDSE7`),
            KEY `FK_SE2_IDSA8` (`SE2_IDSA8`)
          )
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('SE2');
    }
};
