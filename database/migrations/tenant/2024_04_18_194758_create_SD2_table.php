<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
        CREATE TABLE `SD2` (
            `SD2_ID` int NOT NULL AUTO_INCREMENT,
            `SD2_IDEA1` int NOT NULL COMMENT 'Id da empresa',
            `SD2_IDSF2` int NOT NULL COMMENT 'Cabecalho da NF',
            `SD2_IDSB1` int NOT NULL COMMENT 'Id do produto',
            `SD2_IDSB2` int DEFAULT '0',
            `SD2_IDSBW` int DEFAULT '0' COMMENT 'ID do Armazem',
            `SD2_IDSB1_SBK` int DEFAULT '0' COMMENT 'Equivale ao SBK_IDSB1',
            `SD2_IDSB2_SBK` int DEFAULT '0' COMMENT 'Equivale ao SBK_IDSB2',
            `SD2_IDSA3` int NOT NULL COMMENT 'Id do vendedor',
            `SD2_IDSED` int NOT NULL DEFAULT '0',
            `SD2_IDSC2` int NOT NULL DEFAULT '0',
            `SD2_IDSC5` int DEFAULT '0' COMMENT 'ID do Orçamento',
            `SD2_IDSC6` int DEFAULT '0' COMMENT 'ID do Item do Orcamento',
            `SD2_IDCFO` int DEFAULT NULL,
            `SD2_IDAS2` int DEFAULT '0' COMMENT 'ID do Item da Assinatura',
            `SD2_IDSD6` int DEFAULT NULL COMMENT 'Id do Item do Contrato',
            `SD2_IDSF1GUIA` int DEFAULT NULL COMMENT 'ID da Guia que recolheu o imposto',
            `SD2_IDSB1_UMComercial` int DEFAULT '0' COMMENT 'ID da Unid.Medida Comercial (que vai na nota)',
            `SD2_Doc` varchar(30) COLLATE latin1_general_ci DEFAULT '',
            `SD2_TipoMovto` varchar(3) COLLATE latin1_general_ci DEFAULT NULL,
            `SD2_Quant` decimal(11,3) DEFAULT '0.000' COMMENT 'Quantidade do item',
            `SD2_PrcUni` decimal(18,8) DEFAULT '0.00000000',
            `SD2_ValItem` decimal(11,2) DEFAULT '0.00',
            `SD2_ValItemNFe` decimal(11,2) DEFAULT '0.00' COMMENT 'Valor na NFe',
            `SD2_QuantComercial` decimal(11,3) DEFAULT '0.000' COMMENT 'Qt. Comercial (que vai na nota)',
            `SD2_PrcUniComercial` decimal(18,8) DEFAULT '0.00000000' COMMENT 'Preco Unit. Comercial',
            `SD2_QuantVol` decimal(10,3) DEFAULT '0.000' COMMENT 'Quantidade de volumes',
            `SD2_Custo` decimal(30,5) DEFAULT '0.00000',
            `SD2_ValFrete` decimal(10,2) DEFAULT '0.00' COMMENT 'Frete do item',
            `SD2_ValSeguro` decimal(10,2) DEFAULT '0.00' COMMENT 'Seguro do item',
            `SD2_ValDesconto` decimal(10,2) DEFAULT '0.00' COMMENT 'Desconto do item',
            `SD2_ValDespesa` decimal(10,2) DEFAULT '0.00' COMMENT 'Despesas acessorias do item',
            `SD2_ValJuros` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor dos juros financeiros de parcelamento rateados por item',
            `SD2_SitTribISS` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT '1=tributa;2=nao tributa',
            `SD2_PCredSimples` decimal(4,2) DEFAULT '0.00' COMMENT 'Alíquota aplicável de cálculo do crédito',
            `SD2_CredICMSSimples` decimal(10,2) DEFAULT '0.00' COMMENT 'Crédito do ICMS que pode ser aproveitado',
            `SD2_PMargemICMS_STRet` decimal(5,2) DEFAULT '0.00' COMMENT '% Margem de valor adicional ICMS ST retido anteriormente',
            `SD2_BaseICMS_STRet` decimal(11,2) DEFAULT '0.00' COMMENT 'BC ICMS ST retido anteriormente',
            `SD2_PST` decimal(7,4) DEFAULT '0.0000' COMMENT 'Alíquota suportada pelo Consumidor Final',
            `SD2_ValICMS_STRet` decimal(10,2) DEFAULT '0.00' COMMENT 'ICMS ST retido anteriormente',
            `SD2_BaseFCPSTRet` double(15,2) DEFAULT '0.00' COMMENT 'Valor da Base de Cálculo do FCP retido anteriormente por ST',
            `SD2_PFCPSTRet` decimal(7,4) DEFAULT '0.0000' COMMENT 'Percentual do FCP retido anteriormente por Substituição Tributária',
            `SD2_ValFCPSTRet` double(15,2) DEFAULT '0.00' COMMENT 'Valor do FCP retido por Substituição Tributária',
            `SD2_PRedBaseCalcEfet` decimal(4,2) DEFAULT NULL COMMENT 'Percentual de redução da base de cálculo efetiva',
            `SD2_ValBaseCalcEfet` decimal(10,2) DEFAULT NULL COMMENT 'Valor da base de cálculo efetiva',
            `SD2_PICMSEfet` decimal(4,2) DEFAULT NULL COMMENT 'Alíquota do ICMS efetivo',
            `SD2_ValICMSEfet` decimal(10,2) DEFAULT NULL COMMENT 'Valor do ICMS efetivo',
            `SD2_BaseICMS_STDestino` decimal(11,2) DEFAULT '0.00' COMMENT 'BC ICMS ST da UF destino',
            `SD2_ValICMS_STDestino` decimal(10,2) DEFAULT '0.00' COMMENT 'ICMS ST da UF destino',
            `SD2_CompoeTotalNF` varchar(1) COLLATE latin1_general_ci DEFAULT '1' COMMENT 'Valor do item compoe o total da Nota? 0-nao 1-sim',
            `SD2_SitTribICMS` varchar(3) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Sit. Trib. ICMS',
            `SD2_ModBaseICMS` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Modalid. de determ. da BC ICMS',
            `SD2_PRedBaseICMS` decimal(9,4) DEFAULT '0.0000' COMMENT '% Redução da BC ICMS',
            `SD2_PICMS_STM` decimal(6,2) DEFAULT '0.00' COMMENT '% Aliquota do ICMS-ST Carga M�dia',
            `SD2_BaseImp` decimal(11,2) DEFAULT '0.00' COMMENT 'BC ICMS',
            `SD2_PImp` decimal(5,2) DEFAULT '0.00',
            `SD2_ValImp` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do ICMS',
            `SD2_BaseFCP` double(15,2) DEFAULT '0.00' COMMENT 'Valor da Base de Cálculo do FCP',
            `SD2_PFCP` decimal(7,4) DEFAULT '0.0000' COMMENT 'Percentual do Fundo de Combate à Pobreza (FCP)',
            `SD2_ValFCP` double(15,2) DEFAULT '0.00' COMMENT 'Valor do Fundo de Combate à Pobreza (FCP)',
            `SD2_ValICMSDeson` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do ICMS Desonerado',
            `SD2_MotivoDesonICMS` varchar(2) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Motivo da desoneracao do ICMS',
            `SD2_ValICMSOp` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do ICMS da Operacao',
            `SD2_PDif` decimal(9,4) DEFAULT '0.0000' COMMENT '% do Diferimento',
            `SD2_ValICMSDif` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do ICMS diferido',
            `SD2_PBaseOpPropria` decimal(5,2) DEFAULT '0.00' COMMENT '% BC da operacao propria',
            `SD2_ModBaseICMS_ST` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Modalid. de determ. da BC ICMS ST',
            `SD2_PRedBaseICMS_ST` decimal(9,4) DEFAULT '0.0000' COMMENT '% Redução da BC ICMS',
            `SD2_PMargemICMS_ST` decimal(5,2) DEFAULT '0.00' COMMENT '% Margem valor adic. ICMS ST',
            `SD2_NaoAjustaMargem` varchar(1) COLLATE latin1_general_ci DEFAULT '0' COMMENT '1=altera margem do ICMS-ST',
            `SD2_BaseICMS_ST` decimal(11,2) DEFAULT '0.00' COMMENT 'Base de calculo ICMS ST',
            `SD2_PICMS_ST` decimal(4,2) DEFAULT '0.00' COMMENT 'Aliquota ICMS ST',
            `SD2_ValICMS_STBruto` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do ICMS ST',
            `SD2_ValICMS_STLiquido` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do ICMS ST',
            `SD2_ValICMSSubstituto` decimal(13,2) DEFAULT '0.00' COMMENT 'Valor do ICMS próprio do Substituto',
            `SD2_BaseFCPST` double(15,2) DEFAULT '0.00' COMMENT 'Valor da Base de Cálculo do FCP retido por Substituição Tributária',
            `SD2_PFCPST` decimal(7,4) DEFAULT '0.0000' COMMENT 'Percentual do FCP retido por Substituição Tributária',
            `SD2_ValFCPST` double(15,2) DEFAULT '0.00' COMMENT 'Valor do FCP retido por Substituição Tributária',
            `SD2_IDSAE_ST` int DEFAULT '0' COMMENT 'ID da UF do ICMS ST devido na operacao',
            `SD2_BaseUFDestino` decimal(11,2) DEFAULT '0.00' COMMENT 'Valor da Base de Calculo do ICMS na UF do destinatario',
            `SD2_BaseFCPUFDest` double(15,2) DEFAULT '0.00' COMMENT 'Valor da BC FCP na UF de destino',
            `SD2_PICMSFCP` decimal(4,2) DEFAULT '0.00' COMMENT 'Percentual do ICMS relativo ao Fundo de Combate a Pobreza (FCP) na UF de destino',
            `SD2_PICMSUFDestino` decimal(4,2) DEFAULT '0.00' COMMENT 'Aliquota interna da UF de destino',
            `SD2_PICMSInterest` decimal(4,2) DEFAULT '0.00' COMMENT 'Aliquota interestadual das UF envolvidas',
            `SD2_PICMSInterestPart` decimal(5,2) DEFAULT '0.00' COMMENT 'Percentual provisorio de partilha do ICMS Interestadual',
            `SD2_ValFCPUFDest` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do ICMS relativo ao Fundo de Combate a Pobreza (FCP) da UF de destino',
            `SD2_ValICMSUFDest` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do ICMS Interestadual para a UF de destino',
            `SD2_ValICMSUFRemet` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do ICMS Interestadual para a UF do remetente',
            `SD2_CEST` varchar(7) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Codigo Especificador da Substituicao Tributaria – CEST',
            `SD2_IndEscala` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Indicador de Escala Relevante (S/N)',
            `SD2_CNPJFab` varchar(14) COLLATE latin1_general_ci DEFAULT '' COMMENT 'CNPJ do Fabricante da Mercadoria',
            `SD2_CodBenef` varchar(10) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Código de Benefício Fiscal na UF aplicado ao item',
            `SD2_SitTribIPI` varchar(2) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Sit. Tribut. IPI',
            `SD2_ClsEnquadrIPI` varchar(5) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Classe de enquadramento IPI',
            `SD2_CodEnquadrIPI` varchar(3) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Codigo de enquadramento IPI',
            `SD2_CNPJProdIPI` varchar(14) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'CNPJ do produtor',
            `SD2_CodSeloIPI` varchar(60) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Codigo do selo de controle',
            `SD2_QuantSeloIPI` decimal(12,0) DEFAULT '0' COMMENT 'Quant. do selo de controle',
            `SD2_TipoCalcIPI` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Tipo de calculo IPI: P ou V',
            `SD2_BaseIPI` decimal(11,2) DEFAULT '0.00' COMMENT 'Base de calculo IPI',
            `SD2_TribISSQN` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Tributação ISSQN: N-Normal, R-Retida, S-Substituta, I-Isenta',
            `SD2_BaseISSQN` decimal(11,2) DEFAULT '0.00' COMMENT 'Base de calculo ISSQN',
            `SD2_PISSQN` decimal(8,4) DEFAULT '0.0000' COMMENT 'Aliquota ISSQN',
            `SD2_IDSVC` int DEFAULT NULL COMMENT 'ID do Servico QN',
            `SD2_CodServQN` varchar(4) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Codigo do Serviço QN',
            `SD2_IDSAEQN` int DEFAULT '0' COMMENT 'ID do Estado de ocorrencia',
            `SD2_IDSAMQN` int DEFAULT '0' COMMENT 'ID do Municipio de ocorrencia',
            `SD2_ValISSQN` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do ISSQN',
            `SD2_PRedBaseINSS` decimal(4,2) DEFAULT '0.00' COMMENT '% Redução BC INSS',
            `SD2_BaseINSS` decimal(11,2) DEFAULT '0.00' COMMENT 'Base de Calculo INSS',
            `SD2_PINSS` decimal(10,2) NOT NULL DEFAULT '0.00',
            `SD2_ValINSS` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do INSS',
            `SD2_TextoINSS` text COLLATE latin1_general_ci COMMENT 'Texto livre para o INSS',
            `SD2_PAproxFed` decimal(5,2) DEFAULT '0.00' COMMENT '% Aproximado de tributos federais',
            `SD2_PAproxEst` decimal(5,2) DEFAULT '0.00' COMMENT '% Aproximado de tributos estaduais',
            `SD2_PAproxMun` decimal(5,2) DEFAULT '0.00' COMMENT '% Aproximado de tributos municipais',
            `SD2_ValAproxFed` decimal(11,2) DEFAULT '0.00' COMMENT 'Valor aproximado de tributos federais',
            `SD2_ValAproxEst` decimal(11,2) DEFAULT '0.00' COMMENT 'Valor aproximado de tributos estaduais',
            `SD2_ValAproxMun` decimal(11,2) DEFAULT '0.00' COMMENT 'Valor aproximado de tributos municipais',
            `SD2_PIPI` decimal(4,2) DEFAULT '0.00',
            `SD2_QuantUnidIPI` decimal(10,4) DEFAULT '0.0000' COMMENT 'Quant. total na unidade padrao',
            `SD2_ValUnidIPI` decimal(10,4) DEFAULT '0.0000' COMMENT 'Valor por unidade',
            `SD2_ValIPI` decimal(10,2) DEFAULT '0.00',
            `SD2_PDevol` decimal(5,2) DEFAULT '0.00' COMMENT 'Percentual da mercadoria devolvida',
            `SD2_ValIPIDevol` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do IPI devolvido',
            `SD2_SitTribPIS` varchar(2) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Sit. Tribut. PIS',
            `SD2_TipoCalcPIS` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Tipo de calculo PIS: P ou V',
            `SD2_BasePIS` decimal(11,2) DEFAULT '0.00' COMMENT 'Base de calculo PIS',
            `SD2_PPIS` decimal(4,2) DEFAULT '0.00' COMMENT 'Aliquota PIS em %',
            `SD2_ValUnidPIS` decimal(10,4) DEFAULT '0.0000' COMMENT 'Aliquota PIS em reais',
            `SD2_QuantUnidPIS` decimal(10,4) DEFAULT '0.0000' COMMENT 'Quant. vendida PIS',
            `SD2_ValPIS` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do PIS',
            `SD2_TipoCalcPIS_ST` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Tipo de calculo PIS ST: P ou V',
            `SD2_BasePIS_ST` decimal(11,2) DEFAULT '0.00' COMMENT 'Base de calculo PIS ST',
            `SD2_PPIS_ST` decimal(4,2) DEFAULT '0.00' COMMENT 'Aliquota PIS ST em %',
            `SD2_ValUnidPIS_ST` decimal(10,4) DEFAULT '0.0000' COMMENT 'Aliquota PIS ST em reais',
            `SD2_QuantUnidPIS_ST` decimal(10,4) DEFAULT '0.0000' COMMENT 'Quant. vendida PIS ST',
            `SD2_SitTribCOFINS` varchar(2) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Sit. Tribut. COFINS',
            `SD2_TipoCalcCOFINS` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Tipo de calculo COFINS: P ou V',
            `SD2_BaseCOFINS` decimal(11,2) DEFAULT '0.00' COMMENT 'Base de calculo COFINS',
            `SD2_PCOFINS` decimal(4,2) DEFAULT '0.00' COMMENT 'Aliquota COFINS em %',
            `SD2_ValUnidCOFINS` decimal(10,4) DEFAULT '0.0000' COMMENT 'Aliquota COFINS em reais',
            `SD2_QuantUnidCOFINS` decimal(10,4) DEFAULT '0.0000' COMMENT 'Quant. vendida COFINS',
            `SD2_ValCOFINS` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do COFINS',
            `SD2_TipoCalcCOFINS_ST` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Tipo de calculo COFINS ST: P ou V',
            `SD2_BaseCOFINS_ST` decimal(11,2) DEFAULT '0.00' COMMENT 'Base de calculo COFINS ST',
            `SD2_PCOFINS_ST` decimal(4,2) DEFAULT '0.00' COMMENT 'Aliquota COFINS ST em %',
            `SD2_ValUnidCOFINS_ST` decimal(10,4) DEFAULT '0.0000' COMMENT 'Aliquota COFINS ST em reais',
            `SD2_QuantUnidCOFINS_ST` decimal(10,4) DEFAULT '0.0000' COMMENT 'Quant. vendida COFINS ST',
            `SD2_ValCOFINS_ST` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do COFINS ST',
            `SD2_ValPIS_ST` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do PIS ST',
            `SD2_DataDespacho` datetime DEFAULT '1970-01-01 03:00:00' COMMENT 'Data do despacho',
            `SD2_QuantDespacho` decimal(10,2) DEFAULT '0.00' COMMENT 'Quantidade despachada',
            `SD2_AR` varchar(50) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Aviso de recebimento do despacho',
            `SD2_SemTitulo` varchar(1) COLLATE latin1_general_ci DEFAULT 'N' COMMENT 'S-nao tem titulo (ValItem=0), N-tem titulo',
            `SD2_AtuEst` varchar(1) COLLATE latin1_general_ci DEFAULT '1' COMMENT 'Atualizacao do estoque: 0-Nao atualizado, 1-Atualizado, 2-Nao atualizar estoque',
            `SD2_EstNeg` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Permite Est.Neg.: S-permite, independente do param., N-nao permite, independente do param., ''''-cfme. o parametro',
            `SD2_Flags` double DEFAULT '0' COMMENT '1-Incluir Frete na BC ICMS, 2-Incl Frete BC ICMS_ST, 4-Incl Seguro BC ICMS, 8-Incl Seguro BC ICMS_ST, 16-Incl Desconto BC ICMS, 32-Incl Desconto BC ICMS_ST, 64-Incl Despesa BC ICMS, 128-Incl Despesa BC ICMS_ST, 256-Incl IPI BC ICMS, 512-Incl IPI BC ICMS_S',
            `SD2_IncluirValFreteBaseICMS` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD2_IncluirValFreteBaseICMS_ST` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD2_IncluirValFreteBaseIPI` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD2_IncluirValFreteBasePIS` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD2_IncluirValFreteBaseCOFINS` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD2_IncluirValFreteBaseII` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD2_IncluirValSeguroBaseICMS` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD2_IncluirValSeguroBaseICMS_ST` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD2_IncluirValSeguroBaseIPI` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD2_IncluirValSeguroBasePIS` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD2_IncluirValSeguroBaseCOFINS` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD2_IncluirValSeguroBaseII` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD2_IncluirValDescontoBaseICMS` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD2_IncluirValDescontoBaseICMS_ST` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD2_IncluirValDescontoBaseIPI` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD2_IncluirValDescontoBasePIS` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD2_IncluirValDescontoBaseCOFINS` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD2_IncluirValDescontoBaseII` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD2_IncluirValDespesaBaseICMS` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD2_IncluirValDespesaBaseICMS_ST` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD2_IncluirValDespesaBaseIPI` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD2_IncluirValDespesaBasePIS` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD2_IncluirValDespesaBaseCOFINS` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD2_IncluirValDespesaBaseII` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD2_IncluirValIPIBaseICMS` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD2_IncluirValIPIBaseICMS_ST` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT '0-Nao 1-Sim',
            `SD2_Aprova` varchar(1) COLLATE latin1_general_ci DEFAULT NULL,
            `SD2_DtRefContrato` date DEFAULT NULL COMMENT 'Data Referencia Faturamento de Contrato',
            `SD2_NumPed` varchar(60) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Número do pedido de compra',
            `SD2_ItemPed` varchar(60) COLLATE latin1_general_ci DEFAULT NULL,
            `SD2_InfAdProd` text COLLATE latin1_general_ci COMMENT 'Informacoes adicionais do produto',
            `SD2_ProdEspec` varchar(9) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Tipo de produto especifico',
            `SD2_tpOp` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Tipo da operação de venda do veiculo',
            `SD2_Chassi` varchar(17) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Chassi do veiculo',
            `SD2_cCor` varchar(4) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Cor',
            `SD2_xCor` varchar(40) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Descricao da Cor',
            `SD2_Pot` varchar(4) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Potencia Motor (CV)',
            `SD2_Cilin` varchar(4) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Cilindradas',
            `SD2_PesoL` varchar(9) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Peso Liquido',
            `SD2_PesoB` varchar(9) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Peso Bruto',
            `SD2_nSerieVeic` varchar(9) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Serial (serie)',
            `SD2_tpComb` varchar(2) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Tipo de combustivel RENAVAM',
            `SD2_nMotor` varchar(21) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Numero do motor',
            `SD2_CMT` varchar(9) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Capacidade maxima de tracao',
            `SD2_Dist` varchar(4) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Distancia entre os eixos',
            `SD2_AnoMod` varchar(4) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Ano modelo de fabricacao',
            `SD2_AnoFab` varchar(4) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Ano de fabricacao',
            `SD2_tpPint` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Tipo de pintura',
            `SD2_tpVeic` varchar(2) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Tipo de veiculo RENAVAM',
            `SD2_EspVeic` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Especie de veiculo RENAVAM',
            `SD2_VIN` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Condicao do VIN',
            `SD2_CondVeic` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Condicao do veiculo',
            `SD2_cMod` varchar(6) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Codigo marca modelo RENAVAM',
            `SD2_cCorDENATRAN` varchar(2) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Codigo da cor DENATRAN',
            `SD2_Lota` varchar(3) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Capacidade maxima de lotacao',
            `SD2_tpRest` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Restricao',
            `SD2_CodProdANVISA` varchar(13) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Código de Produto da ANVISA',
            `SD2_MotivoIsencaoANVISA` varchar(255) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Motivo da isenção da ANVISA',
            `SD2_nLote` varchar(20) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Num do lote de medicamentos',
            `SD2_qLote` varchar(11) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Qtde de produtos no lote',
            `SD2_dFab` datetime DEFAULT NULL COMMENT 'Data de fabricacao',
            `SD2_dVal` datetime DEFAULT NULL COMMENT 'Data de validade',
            `SD2_vPMC` varchar(15) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Preco maximo consumidor',
            `SD2_tpArma` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Indicador do tipo de arma de fogo',
            `SD2_nSerieArma` varchar(9) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Numero de serie da arma',
            `SD2_nCano` varchar(9) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Numero de serie do cano',
            `SD2_descr` varchar(256) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Descricao completa da arma',
            `SD2_CodANP` varchar(21) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Codigo ANP',
            `SD2_DescANP` varchar(95) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Descrição do produto conforme ANP',
            `SD2_PGLP` decimal(7,4) DEFAULT '0.0000' COMMENT 'Percentual do GLP derivado do petróleo no produto GLP (cProdANP=210203001)',
            `SD2_PGNn` decimal(7,4) DEFAULT '0.0000' COMMENT 'Percentual de Gás Natural Nacional - GLGNn para o produto GLP (cProdANP=210203001)',
            `SD2_PGNi` decimal(7,4) DEFAULT '0.0000' COMMENT 'Percentual de Gás Natural Importado - GLGNi para o produto GLP (cProdANP=210203001)',
            `SD2_ValPart` double(15,2) DEFAULT '0.00' COMMENT 'Valor de partida (cProdANP=210203001)',
            `SD2_CODIF` varchar(21) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'CODIF - qdo a UF utilizar',
            `SD2_Temp` decimal(16,4) DEFAULT NULL COMMENT 'Qtde combust. faturada a temp. ambiente',
            `SD2_IDSAE_UFCons` varchar(10) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'UF de consumo',
            `SD2_BCProd` decimal(11,2) DEFAULT NULL COMMENT 'Base de calculo da CIDE',
            `SD2_AliqProd` varchar(9) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Aliquota da CIDE',
            `SD2_CIDE` decimal(10,2) DEFAULT NULL COMMENT 'Valor da CIDE',
            `SD2_MOPPNumONU` varchar(4) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Numero da ONU',
            `SD2_MOPPNumRisco` varchar(4) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'número do risco',
            `SD2_MOPPClasse` varchar(4) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Classe do risco',
            `SD2_MOPPSubClasse` varchar(4) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Sub-classe do risco',
            `SD2_MOPPGrpEmbalagem` varchar(4) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Grupo da embalagem',
            `SD2_MOPPClassificado` varchar(10) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Classificado',
            `SD2_MOPPNomeApropriado` varchar(256) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Nome apropriado',
            `SD2_ExigibilidadeISSQN` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Indicador da exigibilidade do ISS',
            `SD2_IncentivoFiscalISSQN` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Indicador de incentivo Fiscal',
            `SD2_NumProcessoSuspISSQN` varchar(30) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Número do processo judicial ou administrativo de suspensão da exigibilidade',
            `SD2_CRIADOR` int unsigned NOT NULL COMMENT 'Id do Criador',
            `SD2_ALTERADOR` int unsigned NOT NULL DEFAULT '0' COMMENT 'Id do último alterador',
            `SD2_DT_INC` datetime NOT NULL COMMENT 'Data de inclusão do registro',
            `SD2_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteração do registro',
            `SD2_STATUS` int NOT NULL DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
            `SD2_debitoDiferente` varchar(256) COLLATE latin1_general_ci DEFAULT NULL,
            `SD2_creditoDiferente` varchar(256) COLLATE latin1_general_ci DEFAULT NULL,
            `SD2_histDiferente` varchar(256) COLLATE latin1_general_ci DEFAULT NULL,
            `SD2_CreditoICMSPresumido` decimal(10,2) DEFAULT NULL COMMENT 'Valor Crédito Presumido  Base Legal: Art. 11, caput do Anexo III do RICMS/2000-SP e; Decreto nº 51.624/2007.\nConsumido apenas na apuração e livros fiscais do ICMS.',
            `SD2_PCreditoICMSPresumido` decimal(4,2) DEFAULT NULL COMMENT 'Aliquota Crédito Presumido  Base Legal: Art. 11, caput do Anexo III do RICMS/2000-SP e; Decreto nº 51.624/2007. \nConsumido apenas na apuração e livros fiscais do ICMS. Obtido do NCM por estado.',
            `SD2_QBcMono` decimal(15,4) DEFAULT NULL,
            `SD2_AdRemICMS` decimal(6,2) DEFAULT NULL,
            `SD2_VIcmsMono` decimal(15,2) DEFAULT NULL,
            `SD2_QBcMonoReten` decimal(15,4) DEFAULT NULL,
            `SD2_AdRemIcmsReten` decimal(7,4) DEFAULT NULL,
            `SD2_VIcmsMonoReten` decimal(15,2) DEFAULT NULL,
            `SD2_PRedAdRem` decimal(5,2) DEFAULT NULL,
            `SD2_MotRedAdRem` decimal(1,0) DEFAULT NULL,
            `SD2_VIcmsMonoOp` decimal(15,2) DEFAULT NULL,
            `SD2_VIcmsMonoDif` decimal(15,2) DEFAULT NULL,
            `SD2_QBcMonoRet` decimal(15,4) DEFAULT NULL,
            `SD2_AdRemIcmsRet` decimal(6,4) DEFAULT NULL,
            `SD2_VIcmsMonoRet` decimal(15,2) DEFAULT NULL,
            PRIMARY KEY (`SD2_ID`),
            KEY `SD2_IDEA1` (`SD2_IDEA1`),
            KEY `FK_SD2_SF2` (`SD2_IDSF2`),
            KEY `FK_SD2_SB1` (`SD2_IDSB1`),
            KEY `FK_SD2_SB2` (`SD2_IDSB2`),
            KEY `FK_SD2_SA3` (`SD2_IDSA3`),
            KEY `FK_SD2_SED` (`SD2_IDSED`),
            KEY `FK_SD2_SC2` (`SD2_IDSC2`),
            KEY `FK_SD2_SC5` (`SD2_IDSC5`),
            KEY `FK_SD2_CFO` (`SD2_IDCFO`),
            KEY `IDSC6` (`SD2_IDSC6`),
            KEY `FK_SD2_SD6` (`SD2_IDSD6`),
            KEY `FK_SD2_SB1_SBK` (`SD2_IDSB1_SBK`),
            KEY `FK_SD2_SB2_SBK` (`SD2_IDSB2_SBK`),
            KEY `FK_SD2_SBW` (`SD2_IDSBW`),
            KEY `FK_SD2_SVC` (`SD2_IDSVC`),
            KEY `IDEA1_TipoMovto` (`SD2_IDEA1`,`SD2_TipoMovto`)
          )
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('SD2');
    }
};
