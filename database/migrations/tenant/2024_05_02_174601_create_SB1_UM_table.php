<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
        CREATE TABLE `SB1_UM` (
          `SB1_UM_ID` int NOT NULL AUTO_INCREMENT,
          `SB1_UM_IDEA1` int NOT NULL COMMENT 'Id da empresa',
          `SB1_UM_Codigo` varchar(6) COLLATE latin1_general_ci DEFAULT NULL,
          `SB1_UM_Desc` varchar(255) COLLATE latin1_general_ci NOT NULL COMMENT 'Descrição',
          `SB1_UM_CRIADOR` int unsigned NOT NULL COMMENT 'Id do Criador',
          `SB1_UM_ALTERADOR` int unsigned NOT NULL DEFAULT '0' COMMENT 'Id do último alterador',
          `SB1_UM_DT_INC` datetime NOT NULL COMMENT 'Data de inclusão do registro',
          `SB1_UM_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteração do registro',
          `SB1_UM_STATUS` int NOT NULL DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
          PRIMARY KEY (`SB1_UM_ID`),
          KEY `SB1_UM_Codigo` (`SB1_UM_IDEA1`,`SB1_UM_Codigo`),
          KEY `SB1_UM_Desc` (`SB1_UM_IDEA1`,`SB1_UM_Desc`)
        )
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sb1_um');
    }
};
