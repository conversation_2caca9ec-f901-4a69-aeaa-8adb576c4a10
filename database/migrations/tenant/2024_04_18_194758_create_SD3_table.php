<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
        CREATE TABLE `SD3` (
            `SD3_ID` int NOT NULL AUTO_INCREMENT,
            `SD3_IDEA1` int NOT NULL COMMENT 'Id da Empresa',
            `SD3_Doc` varchar(255) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Documento',
            `SD3_TipoMovto` varchar(3) COLLATE latin1_general_ci DEFAULT NULL,
            `SD3_NumSeq` int DEFAULT '0' COMMENT 'Numero Sequencial',
            `SD3_Emissao` datetime NOT NULL COMMENT 'Emissao',
            `SD3_Tipo` varchar(1) COLLATE latin1_general_ci NOT NULL COMMENT 'Tipo',
            `SD3_IDSB1` int NOT NULL COMMENT 'ID do Produto',
            `XXX_IDSB1Old` int DEFAULT NULL,
            `SD3_IDSB2` int DEFAULT '0',
            `SD3_IDSBW` int DEFAULT '0' COMMENT 'ID do Armazem',
            `SD3_IDSC2` int NOT NULL COMMENT 'ID da ordem de produção/serviço',
            `SD3_IDSC9` int DEFAULT '0' COMMENT 'ID do Item da Ficha de Producao',
            `SD3_IDSAO` int DEFAULT '0' COMMENT 'ID do Operador',
            `SD3_IDSD1` int DEFAULT NULL COMMENT 'ID do Item de Entrada CCCDI (Terceiro)',
            `SD3_IDSD2` int DEFAULT NULL COMMENT 'ID do Item de Saída CCCDI (Terceiro)',
            `SD3_Quant` decimal(11,3) NOT NULL COMMENT 'Quantidade',
            `SD3_Quant_2` decimal(11,3) DEFAULT '0.000' COMMENT 'Quantidade na 2a. UM',
            `SD3_Custo` decimal(30,5) DEFAULT '0.00000',
            `SD3_TipoCusto` varchar(3) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Tipo do custo: STD-Standard, MED-Médio',
            `SD3_IDSED` int NOT NULL COMMENT 'ID Natureza',
            `SD3_Hist` varchar(255) COLLATE latin1_general_ci NOT NULL COMMENT 'Historico',
            `SD3_Ordem` varchar(9) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Ordem para Recalculo do Custo Medio',
            `SD3_Campo1` longtext COLLATE latin1_general_ci,
            `SD3_Campo2` longtext COLLATE latin1_general_ci,
            `SD3_Campo3` longtext COLLATE latin1_general_ci,
            `SD3_Campo4` longtext COLLATE latin1_general_ci,
            `SD3_Campo5` longtext COLLATE latin1_general_ci,
            `SD3_Campo6` longtext COLLATE latin1_general_ci,
            `SD3_Campo7` longtext COLLATE latin1_general_ci,
            `SD3_Campo8` longtext COLLATE latin1_general_ci,
            `SD3_Campo9` longtext COLLATE latin1_general_ci,
            `SD3_Campo10` longtext COLLATE latin1_general_ci,
            `SD3_CRIADOR` int unsigned NOT NULL COMMENT 'Id do Criador',
            `SD3_ALTERADOR` int unsigned NOT NULL DEFAULT '0' COMMENT 'Id do último alterador',
            `SD3_DT_INC` datetime NOT NULL COMMENT 'Data de inclusão do registro',
            `SD3_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteração do registro',
            `SD3_STATUS` int NOT NULL DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
            `SD3_debitoDiferente` varchar(256) COLLATE latin1_general_ci DEFAULT NULL,
            `SD3_creditoDiferente` varchar(256) COLLATE latin1_general_ci DEFAULT NULL,
            `SD3_histDiferente` varchar(256) COLLATE latin1_general_ci DEFAULT NULL,
            PRIMARY KEY (`SD3_ID`),
            KEY `SD3_Emissao` (`SD3_IDEA1`,`SD3_Emissao`),
            KEY `FK_SD3_SB1` (`SD3_IDSB1`),
            KEY `FK_SD3_SB2` (`SD3_IDSB2`),
            KEY `FK_SD3_SC2` (`SD3_IDSC2`),
            KEY `FK_SD3_SED` (`SD3_IDSED`),
            KEY `SD3_IDSAO` (`SD3_IDSAO`),
            KEY `SD3_IDSC9` (`SD3_IDSC9`),
            KEY `FK_SD3_SBW` (`SD3_IDSBW`),
            KEY `IDSC2_Emissao` (`SD3_IDSC2`,`SD3_Emissao`),
            KEY `IDEA1_Tipo_IDSC2` (`SD3_IDEA1`,`SD3_Tipo`,`SD3_IDSC2`),
            KEY `IDEA1_TipoMovto` (`SD3_IDEA1`,`SD3_TipoMovto`),
            KEY `IDSD1` (`SD3_IDSD1`),
            KEY `IDSD2` (`SD3_IDSD2`)
          )
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('SD3');
    }
};
