<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
CREATE TABLE `MVA` (
  `MVA_ID` int NOT NULL AUTO_INCREMENT,
  `MVA_IDEA1` int DEFAULT NULL,
  `MVA_IDNCM` int NOT NULL,
  `MVA_IDNCM_TRB` int DEFAULT NULL,
  `MVA_IDSAE` int NOT NULL,
  `MVA_PMARGEM` decimal(5,2) DEFAULT NULL,
  `MVA_PReduzido` decimal(5,2) DEFAULT '0.00' COMMENT 'Percentual de MVA Reduzido para as empresas  de SC optantes pelo Simples Nacional',
  `MVA_PRedBaseICMS_ST` decimal(6,4) DEFAULT NULL,
  `MVA_PRedBaseICMS_ST_SN` decimal(5,2) DEFAULT NULL COMMENT 'Percentual do ICMS ST Reduzido para as empresas de GO optantes pelo Simples Nacional',
  `MVA_Convenio` int DEFAULT '0',
  `MVA_AjustaMargem` int DEFAULT '0',
  `MVA_BaseDuplaDIFAL_ICMS_ST` int DEFAULT '0',
  `MVA_CRIADOR` int DEFAULT NULL,
  `MVA_ALTERADOR` int DEFAULT NULL,
  `MVA_DT_INC` datetime DEFAULT NULL,
  `MVA_DT_ALT` datetime DEFAULT NULL,
  PRIMARY KEY (`MVA_ID`),
  UNIQUE KEY `IDEA1+IDNCM+UF` (`MVA_IDEA1`,`MVA_IDNCM`,`MVA_IDSAE`),
  UNIQUE KEY `IDEA1+IDNCM_TRB+IDSAE` (`MVA_IDEA1`,`MVA_IDNCM_TRB`,`MVA_IDSAE`),
  KEY `IDEA1` (`MVA_IDEA1`),
  KEY `IDSAE` (`MVA_IDSAE`),
  KEY `IDNCM` (`MVA_IDNCM`,`MVA_IDSAE`),
  KEY `IDNCM_TRB+IDSAE` (`MVA_IDNCM_TRB`,`MVA_IDSAE`)
);
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('MVA');
    }
};
