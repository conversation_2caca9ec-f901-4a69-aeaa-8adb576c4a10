<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
        CREATE TABLE `SPD` (
            `SPD_ID` int NOT NULL AUTO_INCREMENT,
            `SPD_IDEA1` int DEFAULT '0' COMMENT 'ID Empresa: 0 - Plano de Contas Referencial; no. emp - Plano especÃ­fico',
            `SPD_IDPAI` int DEFAULT '0' COMMENT 'ID do registro da Conta Sintética',
            `SPD_Nivel` varchar(1) COLLATE latin1_general_ci NOT NULL COMMENT 'Nível da Conta',
            `SPD_Natureza` varchar(2) COLLATE latin1_general_ci NOT NULL COMMENT 'Natureza da conta',
            `SPD_Tipo` varchar(1) COLLATE latin1_general_ci NOT NULL DEFAULT 'S' COMMENT 'Tipo de Conta S Sintética A Analitica',
            `SPD_CtaSped` varchar(15) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Identificação da Conta Sped',
            `SPD_Desc` varchar(160) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Descrição da Conta Sped',
            `SPD_ECD_ECF` varchar(3) COLLATE latin1_general_ci DEFAULT NULL,
            `SPD_ANO` varchar(4) COLLATE latin1_general_ci DEFAULT NULL,
            `SPD_CRIADOR` int unsigned NOT NULL COMMENT 'Id do Criador',
            `SPD_ALTERADOR` int unsigned NOT NULL DEFAULT '0' COMMENT 'Id do último alterador',
            `SPD_DT_INC` datetime NOT NULL COMMENT 'Data de inclusão do registro',
            `SPD_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteração do registro',
            `SPD_STATUS` int NOT NULL DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
            PRIMARY KEY (`SPD_ID`),
            KEY `SPD_Desc` (`SPD_Desc`,`SPD_STATUS`)
        )
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('SPD');
    }
};
