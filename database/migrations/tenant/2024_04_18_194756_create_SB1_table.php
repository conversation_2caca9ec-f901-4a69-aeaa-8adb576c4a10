<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
CREATE TABLE `SB1` (
  `SB1_ID` int NOT NULL AUTO_INCREMENT,
  `XXX_IDTemp` int DEFAULT '0' COMMENT 'Campo provisorio para acerto da tabela',
  `SB1_IDEA1` int NOT NULL COMMENT 'Id da empresa',
  `SB1_IDSBA` int NOT NULL COMMENT 'Id da categoria de produto',
  `SB1_IDEV1` int NOT NULL COMMENT 'Id do evento',
  `SB1_IDEV2` int NOT NULL COMMENT 'Id da ocorrencia',
  `SB1_IDSEDD` int DEFAULT '0' COMMENT 'ID da Natureza Padrao de Despesa/Compra/Requisicao',
  `SB1_IDSEDR` int DEFAULT '0' COMMENT 'ID da Natureza Padrao de Receita/Faturamento/Devolucao',
  `SB1_IDNCM` int DEFAULT '0' COMMENT 'ID da NCM',
  `SB1_IDCBF` int DEFAULT NULL COMMENT 'ID do Código de Benefício Fiscal',
  `SB1_IDSVC` int DEFAULT NULL COMMENT 'Codigo do Servico',
  `SB1_IDSPDDes` int DEFAULT '0' COMMENT 'ID da Conta Sped de Despesa ou Compra',
  `SB1_IDSPDRec` int DEFAULT '0' COMMENT 'ID da Conta Sped de Receita ou Faturamento',
  `SB1_IDSPDEst` int DEFAULT '0' COMMENT 'Conta SPED Estoques',
  `SB1_IDSPDImob` int DEFAULT '0' COMMENT 'Conta SPED Imobilizados',
  `SB1_IDSA1` int DEFAULT '0' COMMENT 'ID do Fornecedor padrao',
  `SB1_IDSB1_UM` int DEFAULT '0' COMMENT 'ID da Unidade de medida adotada no inventário',
  `SB1_IDSB1_UM_2` int DEFAULT '0' COMMENT 'ID da 2a. Unidade de Medida no Estoque',
  `SB1_IDICMSPDV` int DEFAULT NULL COMMENT 'Id do Código do ICMS do PDVFlex',
  `SB1_Tipo` varchar(2) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Tipo do Produto/Serviço: PA-Produto Acabado, MP-Materia-Prima, MC-Material de Consumo, RV-Material de Revenda, IM-Imobilizado, SV-Serviço',
  `SB1_CFA` varchar(1) COLLATE latin1_general_ci DEFAULT 'A' COMMENT 'Visivel nas telas de: C-Compras, F-Faturamento, A-Ambos',
  `SB1_Codigo` varchar(25) COLLATE latin1_general_ci DEFAULT '',
  `SB1_EAN` varchar(14) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Codigo EAN',
  `SB1_PartNumber` varchar(20) COLLATE latin1_general_ci DEFAULT '',
  `SB1_CodAtivEcon` varchar(8) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Código indicador correspondente à atividade sujeita a incidência da contribuição Previdenciária sobre a receita bruta.',
  `SB1_ClassItem` varchar(4) COLLATE latin1_general_ci DEFAULT '' COMMENT 'NF Modelo 22 - Codigo de Classificação do Item',
  `SB1_Foto` varchar(20) COLLATE latin1_general_ci DEFAULT NULL,
  `SB1_Desc` varchar(255) COLLATE latin1_general_ci NOT NULL COMMENT 'Descrição',
  `SB1_UM` varchar(6) COLLATE latin1_general_ci DEFAULT NULL,
  `SB1_Modelo` varchar(255) COLLATE latin1_general_ci DEFAULT NULL,
  `SB1_Caracteristicas` text COLLATE latin1_general_ci,
  `SB1_PesoLiquido` decimal(10,3) DEFAULT '0.000' COMMENT 'Peso Liquido',
  `SB1_Peso` decimal(10,3) DEFAULT '0.000' COMMENT 'Peso Bruto',
  `SB1_AtuEst` varchar(1) COLLATE latin1_general_ci NOT NULL,
  `SB1_DifAtualStd` decimal(4,2) DEFAULT '-1.00' COMMENT 'Percentual de diferenÃ§a para atualizar o custo standard a partir de uma compra',
  `SB1_CUStd` decimal(16,8) DEFAULT '0.00000000' COMMENT 'Custo Unitario Standard',
  `SB1_Margem` decimal(10,2) DEFAULT NULL,
  `SB1_Markup` decimal(10,2) DEFAULT '0.00',
  `SB1_Preco` decimal(10,2) DEFAULT '0.00',
  `SB1_PrecoSemDesconto` decimal(10,2) DEFAULT '0.00',
  `SB1_PPedido` int DEFAULT '0',
  `SB1_QB` int DEFAULT '0' COMMENT 'Qt. Base da Estrutura',
  `SB1_AliqICMS` varchar(1) COLLATE latin1_general_ci DEFAULT NULL,
  `SB1_AliqISS` varchar(1) COLLATE latin1_general_ci DEFAULT NULL,
  `SB1_PImp` decimal(4,2) DEFAULT '0.00' COMMENT '% Aliquota ICMS',
  `SB1_PIPI` decimal(4,2) DEFAULT NULL,
  `SB1_PISS` decimal(4,2) DEFAULT NULL,
  `SB1_SitTrib` varchar(3) COLLATE latin1_general_ci DEFAULT NULL,
  `SB1_TribIPI` int DEFAULT NULL,
  `SB1_IncluiReg1400` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Indica os servicos com SB1 marcado deve ser levado para SpedFiscal Bloco 1400 ',
  `SB1_CodigoIPM` varchar(60) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Código de Item para IPM da UF - indice participação dos municipios',
  `SB1_Origem` int DEFAULT NULL COMMENT '0-Nacional, 1-Estrangeira (importação direta), 2-Estrangeira (adquirido merc.interno)',
  `SB1_LojaVirtual` varchar(1) COLLATE latin1_general_ci DEFAULT 'N',
  `SB1_Destaque1` varchar(1) COLLATE latin1_general_ci DEFAULT ' ',
  `SB1_Destaque2` varchar(1) COLLATE latin1_general_ci DEFAULT NULL,
  `SB1_Vitrine` varchar(1) COLLATE latin1_general_ci DEFAULT ' ',
  `SB1_Oferta` varchar(1) COLLATE latin1_general_ci DEFAULT ' ',
  `SB1_AutomComercial` varchar(1) COLLATE latin1_general_ci DEFAULT 'S' COMMENT 'Usado no sistema de Automacao Comercial',
  `SB1_Comprimento` decimal(6,3) DEFAULT '0.000',
  `SB1_Altura` decimal(6,3) DEFAULT '0.000',
  `SB1_Largura` decimal(6,3) DEFAULT '0.000',
  `SB1_QAnt` decimal(10,3) DEFAULT '0.000',
  `SB1_VAnt` decimal(10,2) DEFAULT '0.00',
  `SB1_QAtu` decimal(10,3) DEFAULT '0.000',
  `SB1_VAtu` decimal(10,2) DEFAULT '0.00',
  `SB1_EstNeg` varchar(1) COLLATE latin1_general_ci DEFAULT ' ' COMMENT '''S''-Aceita Est.Neg., ''N''-Nao aceita, '' ''-pelo Parametro ESTNEG',
  `SB1_ExibeContrOrc` varchar(2) COLLATE latin1_general_ci DEFAULT 'CO' COMMENT 'C-listar no Contrato, O-listar no Orcamento, CO-em ambos, ''''-em nenhum',
  `SB1_InfAdProd` text COLLATE latin1_general_ci COMMENT 'Informacoes adicionais do produto',
  `SB1_AlteradoProducao` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Indica se o produto/serviço poderá ser alterado na OP ou FP',
  `SB1_Campo1` longtext COLLATE latin1_general_ci,
  `SB1_Campo2` longtext COLLATE latin1_general_ci,
  `SB1_Campo3` longtext COLLATE latin1_general_ci,
  `SB1_Campo4` longtext COLLATE latin1_general_ci,
  `SB1_Campo5` longtext COLLATE latin1_general_ci,
  `SB1_Campo6` longtext COLLATE latin1_general_ci,
  `SB1_Campo7` longtext COLLATE latin1_general_ci,
  `SB1_Campo8` longtext COLLATE latin1_general_ci,
  `SB1_Campo9` longtext COLLATE latin1_general_ci,
  `SB1_Campo10` longtext COLLATE latin1_general_ci,
  `SB1_Campo11` longtext COLLATE latin1_general_ci,
  `SB1_Campo12` longtext COLLATE latin1_general_ci,
  `SB1_Campo13` longtext COLLATE latin1_general_ci,
  `SB1_Campo14` longtext COLLATE latin1_general_ci,
  `SB1_Campo15` longtext COLLATE latin1_general_ci,
  `SB1_Campo16` longtext COLLATE latin1_general_ci,
  `SB1_Campo17` longtext COLLATE latin1_general_ci,
  `SB1_Campo18` longtext COLLATE latin1_general_ci,
  `SB1_Campo19` longtext COLLATE latin1_general_ci,
  `SB1_Campo20` longtext COLLATE latin1_general_ci,
  `SB1_Campo21` longtext COLLATE latin1_general_ci,
  `SB1_Campo22` longtext COLLATE latin1_general_ci,
  `SB1_Campo23` longtext COLLATE latin1_general_ci,
  `SB1_Campo24` longtext COLLATE latin1_general_ci,
  `SB1_Campo25` longtext COLLATE latin1_general_ci,
  `SB1_Campo26` longtext COLLATE latin1_general_ci,
  `SB1_Campo27` longtext COLLATE latin1_general_ci,
  `SB1_Campo28` longtext COLLATE latin1_general_ci,
  `SB1_Campo29` longtext COLLATE latin1_general_ci,
  `SB1_Campo30` longtext COLLATE latin1_general_ci,
  `SB1_Campo31` longtext COLLATE latin1_general_ci,
  `SB1_Campo32` longtext COLLATE latin1_general_ci,
  `SB1_Campo33` longtext COLLATE latin1_general_ci,
  `SB1_Campo34` longtext COLLATE latin1_general_ci,
  `SB1_Campo35` longtext COLLATE latin1_general_ci,
  `SB1_Campo36` longtext COLLATE latin1_general_ci,
  `SB1_Campo37` longtext COLLATE latin1_general_ci,
  `SB1_Campo38` longtext COLLATE latin1_general_ci,
  `SB1_Campo39` longtext COLLATE latin1_general_ci,
  `SB1_Campo40` longtext COLLATE latin1_general_ci,
  `SB1_Campo41` longtext COLLATE latin1_general_ci,
  `SB1_Campo42` longtext COLLATE latin1_general_ci,
  `SB1_Campo43` longtext COLLATE latin1_general_ci,
  `SB1_Campo44` longtext COLLATE latin1_general_ci,
  `SB1_Campo45` longtext COLLATE latin1_general_ci,
  `SB1_Campo46` longtext COLLATE latin1_general_ci,
  `SB1_Campo47` longtext COLLATE latin1_general_ci,
  `SB1_Campo48` longtext COLLATE latin1_general_ci,
  `SB1_Campo49` longtext COLLATE latin1_general_ci,
  `SB1_Campo50` longtext COLLATE latin1_general_ci,
  `SB1_DtValidade` datetime DEFAULT NULL,
  `SB1_EnviadoVitrine` varchar(1) COLLATE latin1_general_ci DEFAULT 'N',
  `SB1_EnviadoSysLoja` varchar(1) COLLATE latin1_general_ci DEFAULT 'N' COMMENT 'Indica se ja foi enviado ao Sistema SysLoja',
  `SB1_EnviadoZanthus` varchar(1) COLLATE latin1_general_ci DEFAULT 'N',
  `SB1_Ativo` int DEFAULT '1' COMMENT '1=Ativo, 0=Inativo',
  `SB1_CRIADOR` int unsigned NOT NULL COMMENT 'Id do Criador',
  `SB1_ALTERADOR` int unsigned NOT NULL DEFAULT '0' COMMENT 'Id do último alterador',
  `SB1_DT_INC` datetime NOT NULL COMMENT 'Data de inclusão do registro',
  `SB1_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteração do registro',
  `SB1_STATUS` int NOT NULL DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
  `SB1_Budget` decimal(10,0) DEFAULT '0' COMMENT 'Valor orçado serviço ou produto',
  `SB1_IDMARCA` bigint DEFAULT NULL COMMENT 'Id da Marca/Brand do produto',
  PRIMARY KEY (`SB1_ID`),
  KEY `SB1_Desc` (`SB1_IDEA1`,`SB1_Desc`),
  KEY `SB1_IDEA1` (`SB1_IDEA1`),
  KEY `FK_SB1_SBA` (`SB1_IDSBA`),
  KEY `FK_SB1_EV1` (`SB1_IDEV1`),
  KEY `FK_SB1_EV2` (`SB1_IDEV2`),
  KEY `FK_SB1_SEDD` (`SB1_IDSEDD`),
  KEY `FK_SB1_SEDR` (`SB1_IDSEDR`),
  KEY `FK_SB1_NCM` (`SB1_IDNCM`),
  KEY `FK_SB1_SA1` (`SB1_IDSA1`),
  KEY `FK_SB1_SPDDes` (`SB1_IDSPDDes`),
  KEY `FK_SB1_SPDRec` (`SB1_IDSPDRec`),
  KEY `Desc` (`SB1_Desc`),
  KEY `SB1_CODIGO` (`SB1_IDEA1`,`SB1_Codigo`),
  KEY `SB1_EAN` (`SB1_IDEA1`,`SB1_EAN`),
  KEY `FK_SB1_SVC` (`SB1_IDSVC`),
  KEY `FK_SB1_UM` (`SB1_IDSB1_UM`),
  KEY `FK_SB1_UM_2` (`SB1_IDSB1_UM_2`),
  KEY `SB1_FOTO` (`SB1_Foto`),
  KEY `FK_SB1_CBF` (`SB1_IDCBF`)
);
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('SB1');
    }
};
