<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
        CREATE TABLE `SE4` (
  `SE4_ID` int NOT NULL AUTO_INCREMENT,
  `SE4_IDEA1` int NOT NULL,
  `SE4_Tipo` char(1) NOT NULL,
  `SE4_IDSE12` int DEFAULT NULL,
  `SE4_IDSE5` int DEFAULT NULL,
  `SE4_IDExtrato` varchar(255) NOT NULL,
  `SE4_MemoExtrato` varchar(255) NOT NULL,
  PRIMARY KEY (`SE4_ID`)
          )
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('SE4');
    }
};
