<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
        CREATE TABLE `SF3` (
            `SF3_ID` int NOT NULL AUTO_INCREMENT,
            `SF3_IDEA1` int NOT NULL,
            `SF3_IDSF1` int NOT NULL DEFAULT '0' COMMENT 'ID da NF de Compras',
            `SF3_IDSF2` int NOT NULL DEFAULT '0' COMMENT 'ID da NF de Faturamento',
            `SF3_ModeloNF` varchar(2) COLLATE latin1_general_ci DEFAULT '55' COMMENT 'Modelo da NF',
            `SF3_Serie` varchar(3) COLLATE latin1_general_ci NOT NULL COMMENT 'Serie da NF',
            `SF3_EmissaoPropria` int DEFAULT NULL COMMENT '0-NF emitida pelo Fornecedor, 1-emitida pela Emppresa',
            `SF3_NatOp` varchar(60) COLLATE latin1_general_ci NOT NULL COMMENT 'Natureza da Operacao',
            `SF3_IDCFO` int NOT NULL COMMENT 'Id CFOP',
            `SF3_DtEntSai` datetime  COMMENT 'Data da Entrada/Saida da mercadoria',
            `SF3_HrEmissao` time DEFAULT '00:00:00' COMMENT 'Hora da emissão da nota',
            `SF3_HrEntSai` time DEFAULT '00:00:00' COMMENT 'Hora da entrada e saida da nota',
            `SF3_DtPrestServ` date  COMMENT 'Data da prestacao do servico',
            `SF3_IndFinal` varchar(1) COLLATE latin1_general_ci DEFAULT '0' COMMENT 'Oper.c/Cons.Final: 0-Normal, 1-Cons.Final',
            `SF3_IndPres` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Indic.presenca do comprador no estabel.comercial',
            `SF3_IndIntermed` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Indicador de intermediador/marketplace: 0=Operação sem intermediador (em site ou plataforma própria) 1=Operação em site ou plataforma de terceiros (intermediadores/marketplace)',
            `SF3_CNPJIntermed` varchar(14) COLLATE latin1_general_ci DEFAULT '' COMMENT 'CNPJ do Intermediador da Transação (agenciador, plataforma de delivery, marketplace e similar) de serviços e de negócios',
            `SF3_IdCadIntTran` varchar(60) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Identificador cadastrado no intermediador. Nome do usuário ou identificação do perfil do vendedor no site do intermediador (agenciador, plataforma de delivery, marketplace e similar) de serviços e de negócios.',
            `SF3_Recopi` varchar(20) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Numero do RECOPI',
            `SF3_BaseICMS` decimal(11,2) NOT NULL COMMENT 'Base de Calculo ICMS',
            `SF3_ValICMS` decimal(10,2) NOT NULL COMMENT 'Valor do ICMS',
            `SF3_ValICMSDeson` decimal(10,2) DEFAULT NULL COMMENT 'Valor do ICMS Desonerado',
            `SF3_ValFCP` double(15,2) DEFAULT '0.00' COMMENT 'Valor Total do FCP (Fundo de Combate à Pobreza)',
            `SF3_BaseICMS_ST` decimal(11,2) NOT NULL COMMENT 'Base de Calculo ICMS ST',
            `SF3_ValICMS_ST` decimal(10,2) NOT NULL COMMENT 'Valor do ICMS ST',
            `SF3_ValFCPST` double(15,2) DEFAULT '0.00' COMMENT 'Valor Total do FCP (Fundo de Combate à Pobreza) retido por substituição tributária',
            `SF3_ValFCPSTRet` double(15,2) DEFAULT '0.00' COMMENT 'Valor Total do FCP retido anteriormente por Substituição Tributária',
            `SF3_ValFCPUFDest` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do ICMS relativo ao Fundo de Combate a Pobreza (FCP) da UF de destino',
            `SF3_ValICMSUFDest` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do ICMS Interestadual para a UF de destino',
            `SF3_ValICMSUFRemet` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do ICMS Interestadual para a UF do remetente',
            `SF3_COD_REC` varchar(15) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Código de receita DIFAL',
            `SF3_ValIPI` decimal(10,2) NOT NULL COMMENT 'Valor do IPI',
            `SF3_ValIPIDevol` double(15,2) DEFAULT '0.00' COMMENT 'Valor Total do IPI devolvido',
            `SF3_BasePIS` decimal(11,2) DEFAULT NULL COMMENT 'Base de Calculo do PIS Retido',
            `SF3_PPIS` decimal(4,2) DEFAULT NULL COMMENT 'Aliquota PIS Retido',
            `SF3_ValPIS` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do PIS (notas de produto), Valor do PIS Retido (notas de serviços)',
            `SF3_PISRetido` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'PIS Retido (1-Retido, 2-Nao retido)',
            `SF3_BaseCOFINS` decimal(11,2) DEFAULT NULL COMMENT 'Base de Calculo do COFINS Retido',
            `SF3_PCOFINS` decimal(4,2) DEFAULT NULL COMMENT 'Aliquota COFINS Retido',
            `SF3_ValCOFINS` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do COFINS (notas de produto), Valor do COFINS Retido (notas de serviços)',
            `SF3_COFINSRetido` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'COFINS Retido (1-Retido, 2-Nao retido)',
            `SF3_BaseIRRF` decimal(11,2) DEFAULT NULL COMMENT 'Base de Calculo do IRRF Retido',
            `SF3_PIRRF` decimal(4,2) DEFAULT NULL COMMENT 'Aliquota IRRF Retido',
            `SF3_ValIRRF` decimal(10,2) DEFAULT NULL COMMENT 'Valor do IRRF Retido',
            `SF3_IRRFRetido` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'IRRF Retido (1-Retido, 2-Nao retido)',
            `SF3_BaseCSLL` decimal(11,2) DEFAULT NULL COMMENT 'Base de Calculo do CSLL Retido',
            `SF3_PCSLL` decimal(4,2) DEFAULT NULL COMMENT 'Aliquota CSLL Retido',
            `SF3_ValCSLL` decimal(10,2) DEFAULT NULL COMMENT 'Valor do CSLL Retido',
            `SF3_CSLLRetido` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'CSLL Retido (1-Retido, 2-Nao retido)',
            `SF3_CheckVal` char(1) COLLATE latin1_general_ci DEFAULT '0' COMMENT 'Checkbox para recalcular Pesos e Volume na aba de Totais',
            `SF3_DestacaImpostos` char(1) COLLATE latin1_general_ci DEFAULT '1' COMMENT 'Destaque de PIS/COFINS na DANFE',
            `SF3_ValFrete` decimal(10,2) NOT NULL COMMENT 'Valor do Frete',
            `SF3_ValSeguro` decimal(10,2) NOT NULL COMMENT 'Valor do Seguro',
            `SF3_ValDesconto` decimal(10,2) NOT NULL COMMENT 'Valor do Desconto',
            `SF3_ValDespesa` decimal(10,2) NOT NULL COMMENT 'Valor da Despesa',
            `SF3_ValJuros` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor dos juros financeiros de parcelamento rateados por item',
            `SF3_ValItem` decimal(11,2) NOT NULL COMMENT 'Valor Total dos Produtos e Servicos',
            `SF3_ValNF` decimal(11,2) NOT NULL COMMENT 'Valor Total da NF',
            `SF3_CodServ` varchar(10) COLLATE latin1_general_ci NOT NULL COMMENT 'Codigo do Servico',
            `SF3_ViasPublicas` varchar(1) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Indica se o Serviço foi prestado em vias publicas: 1-Sim, 2-Nao (Barueri)',
            `SF3_CodTribMunicipio` varchar(20) COLLATE latin1_general_ci NOT NULL COMMENT 'Codigo de Tributação no Municipio',
            `SF3_ISSRetido` varchar(1) COLLATE latin1_general_ci NOT NULL COMMENT 'ISS Retido (1-Retido, 2-Nao retido)',
            `SF3_BaseISSQN` decimal(11,2) DEFAULT '0.00' COMMENT 'Base de calculo ISSQN',
            `SF3_ValISSQN` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT 'Valor do ISS QN',
            `SF3_ValPIS_ISSQN` decimal(10,2) DEFAULT '0.00' COMMENT 'PIS sobre Serviços QN',
            `SF3_ValCOFINS_ISSQN` decimal(10,2) DEFAULT '0.00' COMMENT 'COFINS sobre Serviços QN',
            `SF3_ValServQN` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor dos Serviços QN',
            `SF3_ValDespesaAduanII` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor total das Despesas Aduaneiras',
            `SF3_ValII` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor total do Imposto de Importacao',
            `SF3_ValDespesaII` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor total das Despesas de Importação',
            `SF3_ValAproxFed` decimal(11,2) DEFAULT '0.00' COMMENT 'Valor aproximado de tributos federais',
            `SF3_ValAproxEst` decimal(11,2) DEFAULT '0.00' COMMENT 'Valor aproximado de tributos estaduais',
            `SF3_ValAproxMun` decimal(11,2) DEFAULT '0.00' COMMENT 'Valor aproximado de tributos municipais',
            `SF3_IIContidoPreco` varchar(1) COLLATE latin1_general_ci DEFAULT 'N' COMMENT 'S-Valor do II esta contido no preco do produto',
            `SF3_PISContidoPreco` varchar(1) COLLATE latin1_general_ci DEFAULT 'N' COMMENT 'S-Valor do II esta contido no preco do produto',
            `SF3_COFINSContidoPreco` varchar(1) COLLATE latin1_general_ci DEFAULT 'N' COMMENT 'S-Valor do II esta contido no preco do produto',
            `SF3_IDSA4` int NOT NULL COMMENT 'Id Transportadora',
            `SF3_ModFrete` varchar(1) COLLATE latin1_general_ci NOT NULL COMMENT 'Modalidade do Frete: 0-Emitente, 1-Destinatario',
            `SF3_Placa` varchar(7) COLLATE latin1_general_ci NOT NULL COMMENT 'Placa do veiculo',
            `SF3_UFPlaca` varchar(2) COLLATE latin1_general_ci NOT NULL COMMENT 'UF da Placa do veiculo',
            `SF3_ANTT` varchar(20) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Registro Nacional de Transportador de Carga',
            `SF3_IDSA4Redespacho` int DEFAULT '0' COMMENT 'ID da segunda transportadora',
            `SF3_PlacaRedespacho` varchar(7) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Placa - segunda transportadora',
            `SF3_UFPlacaRedespacho` varchar(7) COLLATE latin1_general_ci DEFAULT '' COMMENT 'UF da Placa - segunda transportadora',
            `SF3_QuantVol` decimal(10,2) NOT NULL COMMENT 'Quant. do volume',
            `SF3_EspecieVol` varchar(30) COLLATE latin1_general_ci NOT NULL COMMENT 'Especie do volume',
            `SF3_MarcaVol` varchar(30) COLLATE latin1_general_ci NOT NULL COMMENT 'Marca do volume',
            `SF3_NumeroVol` varchar(30) COLLATE latin1_general_ci NOT NULL COMMENT 'Numero do volume',
            `SF3_PesoLiquido` decimal(13,5) DEFAULT NULL,
            `SF3_PesoBruto` decimal(13,5) DEFAULT NULL,
            `SF3_IDSAEEmit` int NOT NULL,
            `SF3_IDSAMEmit` int NOT NULL,
            `SF3_IDSAEDest` int NOT NULL,
            `SF3_IDSAMDest` int NOT NULL,
            `SF3_IDSAEIncidencia` int DEFAULT NULL COMMENT 'Id do Estado em que incide o imposto',
            `SF3_IDSAMIncidencia` int DEFAULT NULL COMMENT 'Id do Munic�pio em que incide o imposto',
            `SF3_CPFRet` varchar(14) COLLATE latin1_general_ci DEFAULT '' COMMENT 'CNPJ/CPF do Expedidor (local de retirada)',
            `SF3_RazaoSocialRet` varchar(60) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Razão Social do Expedidor (local de retirada)',
            `SF3_EndRet` varchar(50) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Endereço de retirada',
            `SF3_NumeroRet` varchar(10) COLLATE latin1_general_ci DEFAULT '',
            `SF3_ComplementoRet` varchar(50) COLLATE latin1_general_ci DEFAULT '',
            `SF3_BairroRet` varchar(30) COLLATE latin1_general_ci DEFAULT '',
            `SF3_CEPRet` varchar(8) COLLATE latin1_general_ci DEFAULT '',
            `SF3_DDDRet` varchar(4) COLLATE latin1_general_ci DEFAULT '' COMMENT 'DDD',
            `SF3_TelRet` varchar(8) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Telefone',
            `SF3_EMailRet` varchar(60) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Email',
            `SF3_InscrRet` varchar(14) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Inscrição Estadual do Estabelecimento Expedidor',
            `SF3_IDSAPRet` int DEFAULT NULL COMMENT 'ID do País',
            `SF3_IDSAERet` int DEFAULT NULL COMMENT 'ID da UF',
            `SF3_IDSAMRet` int DEFAULT NULL COMMENT 'ID do Municipio',
            `SF3_RazaoSocialEnt` varchar(60) COLLATE latin1_general_ci DEFAULT '' COMMENT 'RazÃ£o Social do EndereÃ§o de Entrega',
            `SF3_EndEnt` varchar(50) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Endereço de entrega',
            `SF3_NumeroEnt` varchar(10) COLLATE latin1_general_ci DEFAULT NULL,
            `SF3_ComplementoEnt` varchar(50) COLLATE latin1_general_ci DEFAULT NULL,
            `SF3_BairroEnt` varchar(30) COLLATE latin1_general_ci DEFAULT NULL,
            `SF3_MunEnt` varchar(30) COLLATE latin1_general_ci DEFAULT NULL,
            `SF3_EstEnt` varchar(2) COLLATE latin1_general_ci DEFAULT NULL,
            `SF3_CEPEnt` varchar(8) COLLATE latin1_general_ci DEFAULT NULL,
            `SF3_DDDEnt` varchar(4) COLLATE latin1_general_ci DEFAULT '' COMMENT 'DDD Entrega',
            `SF3_TelEnt` varchar(8) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Telefone Entrega',
            `SF3_EMailEnt` varchar(60) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Email Entrega',
            `SF3_InscrEnt` varchar(14) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Inscrição Estadual do Estabelecimento Recebedor (entrega)',
            `SF3_IDSAPEnt` int DEFAULT '0' COMMENT 'ID do País de Entrega',
            `SF3_IDSAEEnt` int DEFAULT '0' COMMENT 'ID da UF de Entrega',
            `SF3_IDSAMEnt` int DEFAULT '0' COMMENT 'ID do Municipio de Entrega',
            `SF3_IDSAEEmbarq` int DEFAULT '0' COMMENT 'ID da UF onde ocorrerá o embarque dos produtos',
            `SF3_LocEmbarq` varchar(60) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Local onde ocorrerá o embarque dos produtos',
            `SF3_TipoRPS` varchar(5) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'RPS / RPS-M',
            `SF3_SitRPS` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Situação do RPS',
            `SF3_TipoTribRPS` varchar(3) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Tipo de Tributação de Serviços',
            `SF3_DescServico` text COLLATE latin1_general_ci COMMENT 'Descritivo dos serviços prestados',
            `SF3_PISSServico` decimal(8,4) DEFAULT '0.0000' COMMENT '% ISS',
            `SF3_BaseISS` decimal(11,2) NOT NULL DEFAULT '0.00',
            `SF3_ValISS` decimal(10,2) NOT NULL DEFAULT '0.00',
            `SF3_ValDeducaoISS` decimal(10,2) NOT NULL DEFAULT '0.00',
            `SF3_BaseINSS` decimal(11,2) NOT NULL DEFAULT '0.00',
            `SF3_PINSS` decimal(10,2) NOT NULL DEFAULT '0.00',
            `SF3_ValINSS` decimal(10,2) NOT NULL DEFAULT '0.00',
            `SF3_INSSRetido` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'INSS Retido (1-Retido, 2-Nao retido)',
            `SF3_InfAdic_Tag` text COLLATE latin1_general_ci COMMENT 'Informações complementares de interesse do Contribuinte com tags',
            `SF3_InfAdic` text COLLATE latin1_general_ci COMMENT 'Informações complementares de interesse do Contribuinte',
            `SF3_InfAdicRF` text COLLATE latin1_general_ci COMMENT 'Informações complementares de interesse do Contribuinte definidas na Regra Fiscal',
            `SF3_InfAdicFisco` text COLLATE latin1_general_ci COMMENT 'Informações adicionais de interesse do Fisco',
            `SF3_Finalidade` varchar(1) COLLATE latin1_general_ci DEFAULT '1' COMMENT '1-NFe Normal, 2-NFe Complementar',
            `SF3_CodAut22` varchar(44) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Codigo de Autenticacao Digital (NF mod 22) ou Chave de Acesso (NF mod 55)',
            `SF3_ReciboNFe` varchar(15) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Numero do recibo de transmissao da NFe',
            `SF3_NFeRef` varchar(44) COLLATE latin1_general_ci DEFAULT '' COMMENT 'NFe referenciada: Chave da NFe que esta sendo complementada',
            `SF3_StatusNFe` varchar(3) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Status da NFe (ex.: 100-Autorizado o uso da NFe, 204-Duplicidade de numero)',
            `SF3_GerouNFe` varchar(1) COLLATE latin1_general_ci NOT NULL COMMENT 'Gerou NFe? (S/N)',
            `SF3_StatusGeracaoNFe` text COLLATE latin1_general_ci COMMENT 'Msg retornadas durante a geracao da NFe',
            `SF3_Correcao` longtext COLLATE latin1_general_ci COMMENT 'Texto da Carta de Correcao',
            `SF3_SeqCorrecao` int DEFAULT '0' COMMENT 'Nr. Sequencial do Evento Carta de Correcao',
            `SF3_StatusCorrecao` varchar(3) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Status da ultima transmissao do Evento Carta de Correcao',
            `SF3_NrNFSe` varchar(20) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Numero da NFSe gerada pela prefeitura',
            `SF3_CodigoVerificacao` varchar(50) COLLATE latin1_general_ci DEFAULT NULL,
            `SF3_AcaoCRON` varchar(1) COLLATE latin1_general_ci DEFAULT '0' COMMENT '0-Nada, 1-Transmitir NFe, 2-Consultar status, 3-Enviar Cancelamento NFe, 4-Enviar Carta de Corr.',
            `SF3_DtBloqueioTransmissao` datetime  COMMENT 'Data e hora em que foi bloqueado para trasmissao',
            `SF3_CRIADOR` int unsigned NOT NULL COMMENT 'Id do Criador',
            `SF3_ALTERADOR` int unsigned NOT NULL DEFAULT '0' COMMENT 'Id do último alterador',
            `SF3_DT_INC` datetime NOT NULL COMMENT 'Data de inclusão do registro',
            `SF3_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteração do registro',
            `SF3_STATUS` int NOT NULL DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
            `SF3_CodigoObra` varchar(15) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Codigo da obra (CEI)',
            `SF3_ART` varchar(15) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Anotacao de Responsabilidade Tecnica (ART)',
            `SF3_ProtocoloNFSe` varchar(100) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Protocolo Retorno da NFSe',
            `SF3_IDSF4` int DEFAULT NULL COMMENT 'Id da tabela SF4 utilizada para gerenciar melhor o status da NFSe',
            `SF3_Log` varchar(400) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Log da NFS-e/NF-e',
            `SF3_IDSVC` int DEFAULT NULL COMMENT 'Codigo do Servico',
            `SF3_PercAproxTribFed` decimal(4,2) DEFAULT NULL COMMENT 'Percentual Aprox. Tributos Federais',
            `SF3_PercAproxTribEst` decimal(4,2) DEFAULT NULL COMMENT 'Percentual Aprox. Tributos Estaduais',
            `SF3_PercAproxTribMun` decimal(4,2) DEFAULT NULL COMMENT 'Percentual Aprox. Tributos Municipais',
            `SF3_PercRedBaseISSQN` decimal(4,2) DEFAULT NULL COMMENT 'Percentual de Redução da Base de calculo do ISSQN',
            `SF3_RefECF_MOD` varchar(2) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Preencher com \"2B\", quando se tratar de Cupom Fiscal emitido por máquina registradora (não ECF), com \"2C\", quando se tratar de Cupom Fiscal PDV, ou \"2D\", quando se tratar de Cupom Fiscal (emitido por ECF) (v2.0).',
            `SF3_RefECF_SEQ` int DEFAULT NULL COMMENT 'Informar o número de ordem seqüencial do ECF que emitiu o Cupom Fiscal vinculado à NF-e (v2.0).',
            `SF3_RefECF_COO` int DEFAULT NULL COMMENT 'Informar o Número do Contador de Ordem de Operação - COO vinculado à NF-e (v2.0).',
            `SF3_IDSF7` int DEFAULT NULL,
            `SF3_PARAMETROS` longtext COLLATE latin1_general_ci COMMENT 'Campo para que sejam gravados os dados da NFS-e em JSON',
            `SF3_QBcMono` decimal(15,4) DEFAULT NULL,
            `SF3_VIcmsMono` decimal(15,2) DEFAULT NULL,
            `SF3_QBcMonoReten` decimal(15,4) DEFAULT NULL,
            `SF3_VIcmsMonoReten` decimal(15,2) DEFAULT NULL,
            `SF3_QBcMonoRet` decimal(15,4) DEFAULT NULL,
            `SF3_VIcmsMonoRet` decimal(15,2) DEFAULT NULL,
            `SF3_DT_Inicio_PS` datetime  COMMENT 'data de inicio da prestação de serviço modelo 21/22',
            `SF3_DT_Termino_PS` datetime  COMMENT 'data de termino da prestação de serviço modelo 21/22',
            PRIMARY KEY (`SF3_ID`),
            KEY `FK_SF3_SF1` (`SF3_IDSF1`),
            KEY `FK_SF3_SF2` (`SF3_IDSF2`),
            KEY `FK_SF3_CFO` (`SF3_IDCFO`),
            KEY `FK_SF3_SA4` (`SF3_IDSA4`),
            KEY `FK_SF3_SAEEmit` (`SF3_IDSAEEmit`),
            KEY `FK_SF3_SAMEmit` (`SF3_IDSAMEmit`),
            KEY `FK_SF3_SAEDest` (`SF3_IDSAEDest`),
            KEY `FK_SF3_SAMDest` (`SF3_IDSAMDest`),
            KEY `FK_SF3_SAEEnt` (`SF3_IDSAEEnt`),
            KEY `FK_SF3_SAMEnt` (`SF3_IDSAMEnt`),
            KEY `ReciboNFe` (`SF3_ReciboNFe`),
            KEY `CodAut22` (`SF3_CodAut22`),
            KEY `SF3_IDEA1_DTENTSAI` (`SF3_IDEA1`,`SF3_DtEntSai`),
            KEY `FK_SF3_SAEIncidencia` (`SF3_IDSAEIncidencia`),
            KEY `FK_SF3_SAMIncidencia` (`SF3_IDSAMIncidencia`),
            KEY `AcaoCRON` (`SF3_AcaoCRON`),
            KEY `DtBloqueioTransmissao` (`SF3_DtBloqueioTransmissao`),
            KEY `FK_SF3_SVC` (`SF3_IDSVC`),
            KEY `FK_SF3_SAPEnt` (`SF3_IDSAPEnt`),
            KEY `IDEA1_ModeloNF_NrNFSe` (`SF3_IDEA1`,`SF3_ModeloNF`,`SF3_NrNFSe`)
          )
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('SF3');
    }
};
