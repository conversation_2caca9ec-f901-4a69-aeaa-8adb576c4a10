<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
        CREATE TABLE `SA3` (
            `SA3_ID` int NOT NULL AUTO_INCREMENT,
            `SA3_IDEA1` int NOT NULL COMMENT 'Id da Empresa',
            `SA3_IDSAE` int DEFAULT '0',
            `SA3_IDSAM` int DEFAULT '0',
            `SA3_Ativo` int DEFAULT '1' COMMENT '0=Inativo, 1=Ativo',
            `SA3_Desc` varchar(60) CHARACTER SET latin1 NOT NULL COMMENT 'Nome / Razão Social',
            `SA3_Fantasia` varchar(60) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Nome Fantasia',
            `SA3_End` varchar(50) COLLATE latin1_general_ci NOT NULL COMMENT ' ',
            `SA3_Numero` varchar(10) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Número',
            `SA3_Complemento` varchar(50) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Complemento',
            `SA3_DDI` varchar(3) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'DDI',
            `SA3_DDDTel` varchar(4) COLLATE latin1_general_ci DEFAULT '' COMMENT 'DDD Tel',
            `SA3_Tel` varchar(13) COLLATE latin1_general_ci NOT NULL COMMENT ' ',
            `SA3_DDDCel` varchar(4) COLLATE latin1_general_ci DEFAULT '' COMMENT 'DDD Cel',
            `SA3_TelCel` varchar(13) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Celular',
            `SA3_DDDFax` varchar(4) COLLATE latin1_general_ci DEFAULT '' COMMENT 'DDD Fax',
            `SA3_Fax` varchar(13) COLLATE latin1_general_ci NOT NULL COMMENT ' ',
            `SA3_EMail` varchar(60) COLLATE latin1_general_ci NOT NULL COMMENT ' ',
            `SA3_Bairro` varchar(30) COLLATE latin1_general_ci NOT NULL COMMENT ' ',
            `SA3_Mun` varchar(20) COLLATE latin1_general_ci NOT NULL COMMENT ' ',
            `SA3_Est` varchar(2) COLLATE latin1_general_ci NOT NULL COMMENT ' ',
            `SA3_CEP` varchar(8) COLLATE latin1_general_ci NOT NULL COMMENT ' ',
            `SA3_CPF` varchar(14) COLLATE latin1_general_ci NOT NULL COMMENT ' ',
            `SA3_PComis` decimal(5,2) DEFAULT NULL,
            `SA3_PMaxDesconto` decimal(5,2) DEFAULT NULL COMMENT '% máximo de desconto que o Vendedor pode conceder',
            `SA3_EnviadoVitrine` varchar(1) COLLATE latin1_general_ci DEFAULT 'N',
            `SA3_EnviadoSysLoja` varchar(1) COLLATE latin1_general_ci DEFAULT 'N' COMMENT 'Indica se ja foi enviado ao Sistema SysLoja',
            `SA3_EnviadoZanthus` varchar(1) COLLATE latin1_general_ci DEFAULT 'N' COMMENT 'Indica se ja foi enviado ao Sistema Zanthus',
            `SA3_IDEA2` int DEFAULT '0' COMMENT 'ID do Usuario',
            `SA3_CRIADOR` int unsigned NOT NULL COMMENT 'Id do Criador',
            `SA3_ALTERADOR` int unsigned NOT NULL DEFAULT '0' COMMENT 'Id do último alterador',
            `SA3_DT_INC` datetime NOT NULL COMMENT 'Data de inclusão do registro',
            `SA3_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteração do registro',
            `SA3_STATUS` int NOT NULL DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
            `SA3_Senha` varchar(20) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Senha Usuario PDVServer',
            PRIMARY KEY (`SA3_ID`),
            KEY `SA3_Desc` (`SA3_IDEA1`,`SA3_Desc`),
            KEY `IDEA1_IDEA2` (`SA3_IDEA1`,`SA3_IDEA2`),
            KEY `SA3_IDSAE` (`SA3_IDSAE`),
            KEY `SA3_IDSAM` (`SA3_IDSAM`)
          );
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('SA3');
    }
};
