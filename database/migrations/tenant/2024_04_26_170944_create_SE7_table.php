<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
CREATE TABLE `SE7` (
  `SE7_ID` int NOT NULL AUTO_INCREMENT,
  `SE7_IDEA1` int NOT NULL COMMENT 'Id da Empresa',
  `SE7_IDSA1` int DEFAULT NULL COMMENT 'ID do Cliente ou Fornecedor',
  `SE7_Tipo` varchar(1) COLLATE latin1_general_ci NOT NULL COMMENT 'R-Recebto. Antecipado, P-Pagto. Antecipado',
  `SE7_DtEmissao` datetime DEFAULT '1970-01-01 00:00:00',
  `SE7_Doc` varchar(20) COLLATE latin1_general_ci DEFAULT '',
  `SE7_Hist` varchar(255) COLLATE latin1_general_ci DEFAULT NULL,
  `SE7_Valor` decimal(17,2) DEFAULT '0.00' COMMENT 'Valor recebido ou pago antecipadamente',
  `SE7_CRIADOR` int unsigned DEFAULT NULL COMMENT 'Id do Criador',
  `SE7_ALTERADOR` int unsigned DEFAULT '0' COMMENT 'Id do último alterador',
  `SE7_DT_INC` datetime DEFAULT NULL COMMENT 'Data de inclusão do registro',
  `SE7_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteração do registro',
  `SE7_STATUS` int DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
  PRIMARY KEY (`SE7_ID`),
  KEY `IDEA1` (`SE7_IDEA1`),
  KEY `IDEA1_DtEmissao` (`SE7_IDEA1`,`SE7_DtEmissao`),
  KEY `IDEA1_Doc` (`SE7_IDEA1`,`SE7_Doc`),
  KEY `IDSA1` (`SE7_IDSA1`)
)
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('IE1');
    }
};
