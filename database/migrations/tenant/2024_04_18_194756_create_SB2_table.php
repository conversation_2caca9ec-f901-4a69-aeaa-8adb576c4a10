<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
CREATE TABLE `SB2` (
  `SB2_ID` int NOT NULL AUTO_INCREMENT,
  `SB2_IDEA1` int DEFAULT '0' COMMENT 'Id da empresa',
  `SB2_IDSB1` int DEFAULT '0',
  `XXX_IDSB1Old` int DEFAULT NULL,
  `SB2_Codigo` varchar(25) COLLATE latin1_general_ci DEFAULT '',
  `SB2_EAN` varchar(14) COLLATE latin1_general_ci DEFAULT '' COMMENT 'GTIN (Global Trade Item Number) do produto, antigo código EAN ou código de barras',
  `SB2_EANTrib` varchar(14) COLLATE latin1_general_ci DEFAULT '' COMMENT 'GTIN (Global Trade Item Number) da unidade tributável, antigo código EAN ou código de barras',
  `SB2_ChaveItensVar` varchar(100) COLLATE latin1_general_ci DEFAULT '0',
  `SB2_PPedido` decimal(20,6) DEFAULT '0.000000' COMMENT 'Ponto de Pedido',
  `SB2_CUStd` decimal(16,8) DEFAULT '0.00000000' COMMENT 'Custo Unitario Standard',
  `SB2_CusMed` decimal(16,8) DEFAULT '0.00000000' COMMENT 'Custo Médio',
  `SB2_Preco` decimal(10,2) DEFAULT '0.00',
  `SB2_PrecoPre` decimal(10,2) DEFAULT '0.00',
  `SB2_PrecoPos` decimal(10,2) DEFAULT '0.00',
  `SB2_ValUnidIPI` decimal(10,4) DEFAULT '0.0000' COMMENT 'Valor Unitario da Pauta do IPI',
  `SB2_ValUnidPIS` decimal(10,4) DEFAULT '0.0000' COMMENT 'Valor Unitario da Pauta do PIS',
  `SB2_ValUnidCOFINS` decimal(10,4) DEFAULT '0.0000' COMMENT 'Valor Unitario da Pauta do COFINS',
  `SB2_PrecoSemDesconto` decimal(10,2) DEFAULT '0.00',
  `SB2_QAnt` decimal(10,3) DEFAULT '0.000',
  `SB2_VAnt` decimal(30,5) DEFAULT '0.00000',
  `SB2_QAtu` decimal(10,3) DEFAULT '0.000',
  `SB2_VAtu` decimal(30,5) DEFAULT '0.00000',
  `SB2_QAnt_2` decimal(10,3) DEFAULT '0.000' COMMENT 'Qtde. Ant. na 2a. UM',
  `SB2_QAtu_2` decimal(10,3) DEFAULT '0.000' COMMENT 'Qtde. Atual na 2a. UM',
  `SB2_Local` varchar(100) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Localizacao fisica',
  `SB2_DiasAgendamentoCobranca` int NOT NULL COMMENT 'Numero dias para cobranca na loja virtual',
  `SB2_TermoAceitacao` text COLLATE latin1_general_ci NOT NULL COMMENT 'Gera um texto que deve ser confirmado antes do item ser colocado no carrinho de compras',
  `SB2_DtValidade` datetime COMMENT 'Data de Validade',
  `SB2_DtUltimoAjuste` datetime COMMENT 'Data do ultimo ajuste de custo',
  `SB2_Comprimento` decimal(6,3) DEFAULT '0.000',
  `SB2_Altura` decimal(6,3) DEFAULT '0.000',
  `SB2_Largura` decimal(6,3) DEFAULT '0.000',
  `SB2_PesoLiquido` decimal(12,5) DEFAULT NULL,
  `SB2_Peso` decimal(12,5) DEFAULT NULL,
  `SB2_Ativo` int DEFAULT '1' COMMENT '1=Ativo, 0=Inativo',
  `SB2_CalculaPontoPedido` int DEFAULT '0' COMMENT '0=NÃ£o , 1=Sim',
  `SB2_TempoReposicao` decimal(20,6) DEFAULT '0.000000' COMMENT 'MÃ©dia do tempo de reposiÃ§Ã£o do produto',
  `SB2_ConsumoMedio` decimal(20,6) DEFAULT '0.000000' COMMENT 'MÃ©dia de Consumo do Produto',
  `SB2_DtUltimoCalculoPP` datetime COMMENT 'Data do Ãºltimo cÃ¡lculo do ponto de pedido',
  `SB2_FatorSeguranca` decimal(3,2) DEFAULT '0.00' COMMENT 'Deve ser menor que 1',
  `SB2_Reserva` decimal(18,8) DEFAULT '0.00000000' COMMENT 'Qtd reservada do Produto',
  `SB2_CodigoFCI` varchar(36) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Código da Ficha de Conteúdo de I,portação',
  `SB2_DtGeracaoFCI` datetime COMMENT 'Data da geração da FCI',
  `SB2_ValorParcelaImportadaExterior` decimal(15,2) DEFAULT '0.00' COMMENT 'Valor da Parcela Importada do Exterior',
  `SB2_PImportado` decimal(5,2) DEFAULT '0.00' COMMENT 'Coefiente (percentual) do conteúdo de importação informado pelo contribuinte',
  `SB2_PartNumber` varchar(20) COLLATE latin1_general_ci DEFAULT '',
  `SB2_EnviadoSysLoja` varchar(1) COLLATE latin1_general_ci DEFAULT 'N',
  `SB2_EnviadoZanthus` varchar(1) COLLATE latin1_general_ci DEFAULT 'N',
  `SB2_LoteEconomico` decimal(20,6) DEFAULT '0.000000' COMMENT 'Lote Economico',
  `SB2_Controla_Serie` varchar(1) COLLATE latin1_general_ci DEFAULT 'N' COMMENT 'Controle de Serie (geracao de lote automatico)',
  `SB2_DT_ALT_EST` datetime DEFAULT NULL COMMENT 'Data da Alteração do Estoque',
  `SB2_CRIADOR` int unsigned NOT NULL COMMENT 'Id do Criador',
  `SB2_ALTERADOR` int unsigned NOT NULL DEFAULT '0' COMMENT 'Id do último alterador',
  `SB2_DT_INC` datetime NOT NULL COMMENT 'Data de inclusão do registro',
  `SB2_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteração do registro',
  `SB2_STATUS` int NOT NULL DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
  PRIMARY KEY (`SB2_ID`),
  UNIQUE KEY `IDSB1_ChaveItensVar` (`SB2_IDSB1`,`SB2_ChaveItensVar`,`SB2_IDEA1`),
  KEY `SB2_SB1` (`SB2_IDSB1`),
  KEY `IDEA1_Codigo` (`SB2_IDEA1`,`SB2_Codigo`),
  KEY `IDEA1` (`SB2_IDEA1`)
);
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('SB2');
    }
};
