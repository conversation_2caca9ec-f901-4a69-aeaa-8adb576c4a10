<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
CREATE TABLE `SA6` (
  `SA6_ID` int NOT NULL AUTO_INCREMENT,
  `SA6_IDEA1` int NOT NULL COMMENT 'Id da Empresa',
  `SA6_IDSPD` int DEFAULT '0' COMMENT 'ID da Conta Sped',
  `SA6_IDBL1` int DEFAULT '0' COMMENT 'ID da Linha do Balanco',
  `SA6_Codigo` varchar(3) COLLATE latin1_general_ci NOT NULL COMMENT 'Codigo do Banco',
  `SA6_Desc` varchar(100) COLLATE latin1_general_ci NOT NULL COMMENT 'Descrição',
  `SA6_Agencia` varchar(4) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'numero da agencia',
  `SA6_AgenciaDig` varchar(2) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'digito verificador da agencia',
  `SA6_Conta` varchar(50) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'conta',
  `SA6_ContaDig` varchar(3) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'digito verificador da conta',
  `SA6_Carteira` varchar(5) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'carteira',
  `SA6_CodigoCedente` varchar(20) COLLATE latin1_general_ci NOT NULL,
  `SA6_Convenio` varchar(10) COLLATE latin1_general_ci NOT NULL COMMENT 'Apenas BB',
  `SA6_InstrBoleto1` varchar(255) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'linha 1 das instrucoes do boleto',
  `SA6_InstrBoleto2` varchar(255) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'linha 2 das instrucoes do boleto',
  `SA6_InstrBoleto3` varchar(255) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'linha 3 das instrucoes do boleto',
  `SA6_InstrBoleto4` varchar(255) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'linha 4 das instrucoes do boleto',
  `SA6_InicioNumeracao` bigint DEFAULT NULL,
  `SA6_SaldoAnt` decimal(17,2) DEFAULT '0.00' COMMENT 'Saldo Anterior',
  `SA6_SaldoAtu` decimal(17,2) DEFAULT '0.00' COMMENT 'Saldo Atual',
  `SA6_PJuros` decimal(5,2) DEFAULT '0.00' COMMENT 'P. Juros pago pelo banco por uma aplicação',
  `SA6_FluxoCaixa` varchar(1) COLLATE latin1_general_ci DEFAULT 'S' COMMENT 'S-Inclui no Fluxo de Caixa, N-Nao inclui',
  `SA6_Ativo` varchar(1) COLLATE latin1_general_ci DEFAULT '1' COMMENT '1 - Cadastro Ativo',
  `SA6_CRIADOR` int unsigned NOT NULL COMMENT 'Id do Criador',
  `SA6_ALTERADOR` int unsigned NOT NULL DEFAULT '0' COMMENT 'Id do último alterador',
  `SA6_DT_INC` datetime NOT NULL COMMENT 'Data de inclusão do registro',
  `SA6_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteração do registro',
  `SA6_STATUS` int NOT NULL DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
  `SA6_NumeroRemessa` int DEFAULT NULL COMMENT 'Remessa CNAB PagFor',
  `SA6_NumeroListaDebitos` int DEFAULT NULL COMMENT 'Numero Lista de Debitos - PagFor',
  `SA6_DataUltimaRemessa` datetime DEFAULT NULL COMMENT 'Data Geracao Ultima Remessa CNAB PagFor',
  `SA6_PDV` int DEFAULT '0' COMMENT '0 - NÃO Utiliza no PDV Online - 1 Utiliza no PDV Online',
  `SA6_Byte` int DEFAULT '2' COMMENT 'Controla byte do banco.',
  `SA6_Controle` int DEFAULT NULL COMMENT 'Controla Byte do banco. 0 - Desligado, 1 - Ligado',
  PRIMARY KEY (`SA6_ID`),
  KEY `SA6_Desc` (`SA6_IDEA1`,`SA6_Desc`)
);
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('SA6');
    }
};
