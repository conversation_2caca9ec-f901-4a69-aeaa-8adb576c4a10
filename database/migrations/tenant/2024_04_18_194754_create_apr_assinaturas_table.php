<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
CREATE TABLE `apr_assinaturas` (
  `id` int NOT NULL AUTO_INCREMENT,
  `id_ea1` int NOT NULL COMMENT 'Id da empresa',
  `apr_processo` int NOT NULL,
  `user_id` int NOT NULL,
  `user_perfil` int NOT NULL,
  `data` datetime NOT NULL,
  `aprovado` char(1) NOT NULL DEFAULT 'N',
  `comentario` longtext,
  PRIMARY KEY (`id`),
  KEY `ID_EA1` (`id_ea1`)
);
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('apr_assinaturas');
    }
};
