<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
        CREATE TABLE `spx38940031` (
            `id` int NOT NULL AUTO_INCREMENT,
            `link` int DEFAULT NULL,
            `cpo001` date DEFAULT NULL,
            `cpo002` int DEFAULT NULL,
            `cpo005` int DEFAULT NULL,
            `cpo006` longtext,
            `cpo007` int DEFAULT NULL,
            `cpo008` int DEFAULT NULL,
            `cpo011` longblob,
            `cpo012` int DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `link` (`link`)
          )
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('spx38940012');
    }
};
