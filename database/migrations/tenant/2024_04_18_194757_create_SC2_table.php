<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
CREATE TABLE `SC2` (
  `SC2_ID` int NOT NULL AUTO_INCREMENT,
  `SC2_IDEA1` int NOT NULL COMMENT 'Id da Empresa',
  `SC2_IDSA1` int DEFAULT '0' COMMENT 'ID do Cliente (OS)',
  `SC2_IDSB1` int NOT NULL COMMENT 'Id do produto',
  `SC2_IDSB2` int DEFAULT '0',
  `SC2_IDSBW` int DEFAULT '0' COMMENT 'ID do Armazem',
  `SC2_IDSBO` int DEFAULT '0' COMMENT 'ID da Operacao',
  `SC2_IDSC5` int DEFAULT '0' COMMENT 'A OP/OS pode estar relacionada ao Cabec. OU a um Item de um Orcam.',
  `SC2_IDSC6` int DEFAULT '0' COMMENT 'A OP/OS pode estar relacionada ao Cabec. OU a um Item de um Orcam.',
  `SC2_IDSD6` int DEFAULT NULL COMMENT 'ID do Item do Contrato',
  `SC2_Tipo` varchar(2) COLLATE latin1_general_ci DEFAULT '' COMMENT 'OP: SB1_AtuEst = P, OS: SB1_AtuEst = S',
  `SC2_Doc` varchar(35) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Documento',
  `SC2_NumSeq` int DEFAULT '0' COMMENT 'Numero Sequencial',
  `SC2_Emissao` datetime DEFAULT NULL COMMENT 'Data de emissão',
  `SC2_Previsao` datetime  COMMENT 'Data da Previsão',
  `SC2_Desc` varchar(255) COLLATE latin1_general_ci NOT NULL COMMENT 'Descriï¿½ï¿½o',
  `SC2_Quant` decimal(11,3) NOT NULL COMMENT 'Quantidade prevista',
  `SC2_Valor` decimal(11,2) NOT NULL COMMENT 'Valor previsto (no caso de OS)',
  `SC2_DataProd` datetime DEFAULT NULL COMMENT 'Data da produção da OP ou da execução da OS',
  `SC2_QuantProd` decimal(10,3) DEFAULT NULL COMMENT 'Quantidade produzida',
  `SC2_SaldoAnt` decimal(30,5) DEFAULT '0.00000',
  `SC2_SaldoAtu` decimal(30,5) DEFAULT '0.00000',
  `SC2_SequenciaLote` int DEFAULT NULL COMMENT 'Numero sequencial a ser utilizado nos lotes',
  `SC2_Campo1` longtext COLLATE latin1_general_ci COMMENT 'Dado adicional',
  `SC2_Campo2` longtext COLLATE latin1_general_ci COMMENT 'Dado adicional',
  `SC2_Campo3` longtext COLLATE latin1_general_ci COMMENT 'Dado adicional',
  `SC2_Campo4` longtext COLLATE latin1_general_ci COMMENT 'Dado adicional',
  `SC2_Campo5` longtext COLLATE latin1_general_ci COMMENT 'Dado adicional',
  `SC2_Campo6` longtext COLLATE latin1_general_ci COMMENT 'Dado adicional',
  `SC2_Campo7` longtext COLLATE latin1_general_ci COMMENT 'Dado adicional',
  `SC2_Campo8` longtext COLLATE latin1_general_ci COMMENT 'Dado adicional',
  `SC2_Campo9` longtext COLLATE latin1_general_ci COMMENT 'Dado adicional',
  `SC2_Campo10` longtext COLLATE latin1_general_ci COMMENT 'Dado adicional',
  `SC2_Campo11` longtext COLLATE latin1_general_ci COMMENT 'Dado adicional',
  `SC2_Campo12` longtext COLLATE latin1_general_ci COMMENT 'Dado adicional',
  `SC2_Campo13` longtext COLLATE latin1_general_ci COMMENT 'Dado adicional',
  `SC2_Campo14` longtext COLLATE latin1_general_ci COMMENT 'Dado adicional',
  `SC2_Campo15` longtext COLLATE latin1_general_ci COMMENT 'Dado adicional',
  `SC2_Campo16` longtext COLLATE latin1_general_ci COMMENT 'Dado adicional',
  `SC2_Campo17` longtext COLLATE latin1_general_ci COMMENT 'Dado adicional',
  `SC2_Campo18` longtext COLLATE latin1_general_ci COMMENT 'Dado adicional',
  `SC2_Campo19` longtext COLLATE latin1_general_ci COMMENT 'Dado adicional',
  `SC2_Campo20` longtext COLLATE latin1_general_ci COMMENT 'Dado adicional',
  `SC2_Campo21` longtext COLLATE latin1_general_ci COMMENT 'Dado adicional',
  `SC2_Campo22` longtext COLLATE latin1_general_ci COMMENT 'Dado adicional',
  `SC2_Campo23` longtext COLLATE latin1_general_ci COMMENT 'Dado adicional',
  `SC2_Campo24` longtext COLLATE latin1_general_ci COMMENT 'Dado adicional',
  `SC2_Campo25` longtext COLLATE latin1_general_ci COMMENT 'Dado adicional',
  `SC2_Campo26` longtext COLLATE latin1_general_ci COMMENT 'Dado adicional',
  `SC2_Campo27` longtext COLLATE latin1_general_ci COMMENT 'Dado adicional',
  `SC2_Campo28` longtext COLLATE latin1_general_ci COMMENT 'Dado adicional',
  `SC2_Campo29` longtext COLLATE latin1_general_ci COMMENT 'Dado adicional',
  `SC2_Campo30` longtext COLLATE latin1_general_ci COMMENT 'Dado adicional',
  `SC2_CRIADOR` int unsigned NOT NULL COMMENT 'Id do Criador',
  `SC2_ALTERADOR` int unsigned NOT NULL DEFAULT '0' COMMENT 'Id do último alterador',
  `SC2_DT_INC` datetime NOT NULL COMMENT 'Data de inclusão do registro',
  `SC2_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteração do registro',
  `SC2_STATUS` int NOT NULL DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
  PRIMARY KEY (`SC2_ID`),
  KEY `SC2_Emissao` (`SC2_IDEA1`,`SC2_Emissao`),
  KEY `FK_SC2_SA1` (`SC2_IDSA1`),
  KEY `FK_SC2_SB1` (`SC2_IDSB1`),
  KEY `FK_SC2_SB2` (`SC2_IDSB2`),
  KEY `FK_SC2_SC5` (`SC2_IDSC5`),
  KEY `FK_SC2_SD6` (`SC2_IDSD6`),
  KEY `SC2_IDSBO` (`SC2_IDSBO`),
  KEY `FK_SC2_SBW` (`SC2_IDSBW`),
  KEY `IDEA1_Tipo` (`SC2_IDEA1`,`SC2_Tipo`)
);
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('SC2');
    }
};
