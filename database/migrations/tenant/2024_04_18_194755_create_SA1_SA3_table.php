<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
CREATE TABLE `SA1_SA3` (
  `SA1_SA3_ID` int NOT NULL AUTO_INCREMENT,
  `SA1_SA3_IDEA1` int NOT NULL COMMENT 'Id da Empresa',
  `SA1_SA3_IDSA1` int DEFAULT NULL COMMENT 'Id do Cliente',
  `SA1_SA3_IDSA3` int DEFAULT NULL COMMENT 'Id do Vendedor',
  `SA1_SA3_CRIADOR` int unsigned NOT NULL COMMENT 'Id do Criador',
  `SA1_SA3_ALTERADOR` int unsigned NOT NULL DEFAULT '0' COMMENT 'Id do Ãºltimo alterador',
  `SA1_SA3_DT_INC` datetime NOT NULL COMMENT 'Data de inclusÃ£o do registro',
  `SA1_SA3_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteraÃ§Ã£o do registro',
  `SA1_SA3_STATUS` int NOT NULL DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
  PRIMARY KEY (`SA1_SA3_ID`),
  UNIQUE KEY `IDEA1_IDSA1_IDSA3` (`SA1_SA3_IDEA1`,`SA1_SA3_IDSA1`,`SA1_SA3_IDSA3`),
  KEY `IDSA1` (`SA1_SA3_IDSA1`),
  KEY `IDSA3` (`SA1_SA3_IDSA3`),
  KEY `IDEA1_IDSA1` (`SA1_SA3_IDEA1`,`SA1_SA3_IDSA1`)
)
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('SA1_SA3');
    }
};
