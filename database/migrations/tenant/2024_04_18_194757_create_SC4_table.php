<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
CREATE TABLE `SC4` (
  `SC4_ID` int NOT NULL AUTO_INCREMENT,
  `SC4_IDEA1` int NOT NULL,
  `SC4_IDSC3` int NOT NULL,
  `SC4_IDSB1` int NOT NULL COMMENT ' ',
  `SC4_IDSB2` int DEFAULT '0',
  `SC4_IDSBW` int DEFAULT '0' COMMENT 'ID do Armazem',
  `SC4_IDSED` int DEFAULT '0',
  `SC4_IDSC2` int DEFAULT '0',
  `SC4_IDSB1_UMComercial` int DEFAULT '0' COMMENT 'ID da Unid.Medida Comercial (que vem na nota)',
  `SC4_Hist` varchar(255) COLLATE latin1_general_ci NOT NULL COMMENT 'Histï¿½rico',
  `SC4_Quant` decimal(11,3) DEFAULT '0.000',
  `SC4_PrcUni` decimal(18,8) DEFAULT '0.00000000',
  `SC4_QuantComercial` decimal(11,3) DEFAULT '0.000' COMMENT 'Qt. Comercial (que vem na nota)',
  `SC4_PrcUniComercial` decimal(18,8) DEFAULT '0.00000000' COMMENT 'Preco Unit. Comercial',
  `SC4_ValItem` decimal(11,2) DEFAULT '0.00',
  `SC4_PIPI` decimal(4,2) DEFAULT '0.00' COMMENT '% IPI',
  `SC4_ValIPI` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do IPI',
  `SC4_SitTribICMS` varchar(3) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Sit. Trib. ICMS',
  `SC4_ValImp` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do ICMS',
  `SC4_ValICMSST` decimal(10,2) DEFAULT '0.00' COMMENT 'Valor do ICMS ST',
  `SC4_ValFrete` decimal(10,2) DEFAULT '0.00' COMMENT 'Frete do item',
  `SC4_PercDesconto` decimal(4,2) DEFAULT '0.00' COMMENT 'Percentagem de desconto no valor dos itens (SC4_ValItem)',
  `SC4_ValDesconto` decimal(10,2) DEFAULT '0.00' COMMENT 'Desconto do item',
  `SC4_PrazoValidade` int DEFAULT '0' COMMENT 'Prazo de Validade (em dias)',
  `SC4_CRIADOR` int unsigned NOT NULL COMMENT 'ID do Criador',
  `SC4_ALTERADOR` int unsigned NOT NULL DEFAULT '0' COMMENT 'ID do último alterador',
  `SC4_DT_INC` datetime NOT NULL COMMENT 'Data de inclusão do registro',
  `SC4_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteração do registro',
  `SC4_STATUS` int NOT NULL DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
  `SC4_ValTotal` decimal(10,8) DEFAULT NULL COMMENT 'Valor total pedido',
  `SC4_DataEntrega` date DEFAULT NULL COMMENT 'Data de entrega Informado na Catação de Compras',
  PRIMARY KEY (`SC4_ID`),
  KEY `FK_SC4_SC3` (`SC4_IDSC3`),
  KEY `FK_SC4_SB1` (`SC4_IDSB1`),
  KEY `FK_SC4_SB2` (`SC4_IDSB2`),
  KEY `FK_SC4_SBW` (`SC4_IDSBW`),
  KEY `FK_SC4_SED` (`SC4_IDSED`),
  KEY `FK_SC4_SC2` (`SC4_IDSC2`),
  KEY `SC4_IDEA1` (`SC4_IDEA1`)
);
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('SC4');
    }
};
