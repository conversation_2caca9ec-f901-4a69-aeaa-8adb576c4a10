<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
        CREATE TABLE `spx38940005` (
            `id` int NOT NULL AUTO_INCREMENT,
            `link` int DEFAULT NULL,
            `cpo001` date DEFAULT NULL,
            `cpo002` int DEFAULT NULL,
            `cpo010` int DEFAULT NULL,
            `cpo011` varchar(20) COLLATE latin1_general_ci DEFAULT NULL,
            `cpo012` int DEFAULT NULL,
            `cpo013` int DEFAULT NULL,
            `cpo014` longtext COLLATE latin1_general_ci,
            `cpo015` date DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `link` (`link`)
          )
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('spx38940012');
    }
};
