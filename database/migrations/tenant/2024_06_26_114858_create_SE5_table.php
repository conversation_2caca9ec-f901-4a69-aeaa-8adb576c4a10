<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
        CREATE TABLE `SE5` (
  `SE5_ID` int NOT NULL AUTO_INCREMENT,
  `SE5_IDEA1` int NOT NULL ,
  `SE5_IDSE1` int DEFAULT NULL,
  `SE5_IDSE2` int DEFAULT NULL,
  `SE5_IDSA6` int DEFAULT '0',
  `SE5_IDSE6` int DEFAULT NULL ,
  `SE5_Tipo` varchar(3) ,
  `SE5_Tipo1` varchar(3) ,
  `SE5_TipoBaixa` varchar(1) ,
  `SE5_Cheque` varchar(10) ,
  `SE5_Hist` varchar(255) ,
  `SE5_DtBaixa` datetime DEFAULT NULL ,
  `SE5_DtCredito` datetime DEFAULT NULL ,
  `SE5_Valor` decimal(17,2) NOT NULL ,
  `SE5_ValorDesconto` decimal(17,2) DEFAULT '0.00' ,
  `SE5_ValorAcrescimo` decimal(17,2) DEFAULT '0.00' ,
  `SE5_Reconc` varchar(1) ,
  `SE5_CRIADOR` int unsigned DEFAULT NULL ,
  `SE5_ALTERADOR` int unsigned DEFAULT '0' ,
  `SE5_DT_INC` datetime DEFAULT NULL ,
  `SE5_DT_ALT` datetime DEFAULT NULL ,
  `SE5_STATUS` int DEFAULT '0' ,
  `SE5_TaxaJuros` decimal(9,6) DEFAULT '0.000000',
  `SE5_ValorJuros` decimal(11,2) DEFAULT '0.00',
  PRIMARY KEY (`SE5_ID`)
          )
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('SE5');
    }
};
