<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
CREATE TABLE `SAE` (
  `SAE_ID` int NOT NULL AUTO_INCREMENT,
  `SAE_Est` varchar(2) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Sigla do Estado',
  `SAE_Desc` varchar(20) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Descricao',
  `SAE_Regiao` varchar(2) COLLATE latin1_general_ci DEFAULT NULL,
  `SAE_CodIBGE` varchar(2) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Codigo IBGE',
  `SAE_CRIADOR` int unsigned NOT NULL COMMENT 'Id do <PERSON>riador',
  `SAE_ALTERADOR` int unsigned NOT NULL DEFAULT '0' COMMENT 'Id do último alterador',
  `SAE_DT_INC` datetime NOT NULL COMMENT 'Data de inclusão do registro',
  `SAE_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteração do registro',
  `SAE_STATUS` int NOT NULL DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
  PRIMARY KEY (`SAE_ID`),
  KEY `SAE_STATUS` (`SAE_STATUS`,`SAE_Desc`),
  KEY `Est` (`SAE_Est`),
  KEY `CodIBGE` (`SAE_CodIBGE`)
);
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('SAE');
    }
};
