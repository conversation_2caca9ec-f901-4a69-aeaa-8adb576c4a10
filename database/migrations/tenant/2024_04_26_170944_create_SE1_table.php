<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
CREATE TABLE `SE1` (
  `SE1_ID` int NOT NULL AUTO_INCREMENT,
  `SE1_IDEA1` int NOT NULL COMMENT 'Id da Empresa',
  `SE1_IDSF1` int NOT NULL DEFAULT '0',
  `SE1_IDSE3` int NOT NULL DEFAULT '0',
  `SE1_IDIE2` int NOT NULL DEFAULT '0',
  `SE1_IDDR1` int NOT NULL DEFAULT '0',
  `SE1_IDSE7` int DEFAULT NULL,
  `SE1_Sequencial` int NOT NULL DEFAULT '1' COMMENT 'NÃºmero sequencial da parcela',
  `SE1_IDTipoPagamento` int NOT NULL DEFAULT '0',
  `SE1_IDFormaPagamento` int NOT NULL DEFAULT '0',
  `SE1_IDRef` int DEFAULT NULL COMMENT 'Id origem do registro',
  `SE1_IDSEP` int DEFAULT NULL COMMENT 'ID da Forma de Pagamento',
  `SE1_IDSEA` int DEFAULT NULL COMMENT 'ID da Aglutinacao de Titulos (nos regs. de parcelas da Fatura)',
  `SE1_IDSEARef` int DEFAULT NULL COMMENT 'ID da Aglutinacao de Titulos (nos regs. aglutinados)',
  `SE1_IDSE6` int DEFAULT NULL COMMENT 'ID do Cheque',
  `SE1_IDSA8` int DEFAULT NULL COMMENT 'ID do Cartao de Credito',
  `SE1_Doc` varchar(30) COLLATE latin1_general_ci DEFAULT '',
  `SE1_Fatura` int DEFAULT '0' COMMENT 'Nr. da Fatura (SE1_ID de algum dos titulos aglutinados)',
  `SE1_Hist` varchar(255) COLLATE latin1_general_ci NOT NULL COMMENT 'Histï¿½rico',
  `SE1_CodigoBarras` varchar(60) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Codigo de Barras do boleto',
  `SE1_Vencto` datetime NOT NULL COMMENT ' ',
  `SE1_Tipo` varchar(13) COLLATE latin1_general_ci NOT NULL COMMENT ' ',
  `SE1_tPag` varchar(2) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Meio de Pagamento: 01=Dinheiro, 02=Cheque, 03=Cartão de Crédito, 04=Cartão de Débito, 05=Crédito Loja, 10=Vale Alimentação, 11=Vale Refeição, 12=Vale Presente, 13=Vale Combustível, 15=Boleto Bancário, 16=Depósito Bancário, 17=Pagamento Instantâneo (PIX), 18=Transferência bancária, Carteira Digital, 19=Programa de fidelidade, Cashback, Crédito Virtual, 90= Sem pagamento, 99=Outros',
  `SE1_ComJuros` varchar(1) COLLATE latin1_general_ci NOT NULL DEFAULT 'N' COMMENT 'Se a parcela tem juros embutido',
  `SE1_Valor` decimal(17,2) DEFAULT NULL COMMENT 'Valor',
  `SE1_ValorPago` decimal(17,2) DEFAULT NULL COMMENT 'Valor pago',
  `SE1_ValorDesconto` decimal(17,2) DEFAULT '0.00' COMMENT 'Valor do desconto',
  `SE1_ValorAcrescimo` decimal(17,2) DEFAULT '0.00' COMMENT 'Valor do acrescimo',
  `SE1_ValorPagoFatura` decimal(17,2) DEFAULT '0.00' COMMENT 'Valor das baixas da Fatura, rateado entre os titulos que a compoem',
  `SE1_ValorDescontoFatura` decimal(17,2) DEFAULT '0.00' COMMENT 'Valor dos decontos da Fatura, rateado entre os titulos que a compoem',
  `SE1_ValorAcrescimoFatura` decimal(17,2) DEFAULT '0.00' COMMENT 'Valor dos acrescimos da Fatura, rateado entre os titulos que a compoem',
  `SE1_Agendamento` datetime DEFAULT NULL COMMENT 'Data de geração do arquivo cnab de pagamento',
  `SE1_ArquivoRemessa` varchar(255) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Arquivo cnab de pagamentos que foi feito a remessa do título',
  `SE1_Baixa` datetime DEFAULT NULL,
  `SE1_IDSA6` int DEFAULT '0',
  `SE1_IDSEFBaixa` int DEFAULT '0' COMMENT 'ID da Fatura baixada por este titulo',
  `SE1_Banco` varchar(3) COLLATE latin1_general_ci DEFAULT NULL,
  `SE1_Agencia` varchar(4) COLLATE latin1_general_ci DEFAULT NULL,
  `SE1_AgenciaDig` varchar(3) COLLATE latin1_general_ci DEFAULT '' COMMENT 'Digito da Agencia',
  `SE1_Conta` varchar(50) COLLATE latin1_general_ci DEFAULT NULL,
  `SE1_ContaDig` varchar(3) COLLATE latin1_general_ci DEFAULT NULL,
  `SE1_Cheque` varchar(10) COLLATE latin1_general_ci DEFAULT NULL,
  `SE1_NomeCheque` varchar(50) COLLATE latin1_general_ci DEFAULT NULL,
  `SE1_Finalidade` varchar(10) COLLATE latin1_general_ci DEFAULT NULL,
  `SE1_Tributo` varchar(2) COLLATE latin1_general_ci DEFAULT NULL COMMENT '01=GPS,02=DARF, 03=DARF SIMP, 04=DARJ,05=GARE,07=IPVA;08=DPVAT, 11=FGTS-GFIP',
  `SE1_CodigoPagamento` varchar(10) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'Codigo de recolhimento do tributo',
  `SE1_Nome` varchar(50) COLLATE latin1_general_ci DEFAULT NULL,
  `SE1_CNPJContribuinte` varchar(14) COLLATE latin1_general_ci DEFAULT NULL,
  `SE1_Referencia` varchar(20) COLLATE latin1_general_ci DEFAULT NULL,
  `SE1_PeriodoApuracao` date DEFAULT NULL,
  `SE1_Multa` decimal(11,2) DEFAULT NULL,
  `SE1_OutrosValores` decimal(11,2) DEFAULT NULL,
  `SE1_Reconc` varchar(1) COLLATE latin1_general_ci DEFAULT NULL,
  `SE1_TipoPagRec` int DEFAULT '0' COMMENT '0=Título Normal, 1=Título de Desconto, 2=Título de Acrescimo, 3=Titulo de Desconto por cancelamento',
  `SE1_CRIADOR` int unsigned NOT NULL COMMENT 'Id do Criador',
  `SE1_ALTERADOR` int unsigned NOT NULL DEFAULT '0' COMMENT 'Id do último alterador',
  `SE1_DT_INC` datetime NOT NULL COMMENT 'Data de inclusão do registro',
  `SE1_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteração do registro',
  `SE1_STATUS` int NOT NULL DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
  `SE1_ValorINSS` decimal(11,2) DEFAULT '0.00' COMMENT 'Valor relativo ao INSS, na GPS (ValorINSS = Valor - ValorOutrasEntidades)',
  `SE1_ValorOutrasEntidades` decimal(11,2) DEFAULT '0.00' COMMENT 'Valor relativo a outras entidades, na GPS (ValorOutrasEntidades = Valor - ValorINSS)',
  `SE1_IDTipoDocumento` int DEFAULT NULL COMMENT 'ID Referencia with table TIPODOCUMENTO',
  `SE1_IDSAI` int DEFAULT NULL COMMENT 'ID de ocorrencias Cnab - especifico para pagfor bradesco',
  `SE1_TipoMovimento` int DEFAULT NULL COMMENT 'PagFor - 0=Inclusao 5=Alteracao 9=Exclusao',
  `SE1_FaturaDuplicata` int DEFAULT NULL COMMENT 'PagFor - Numero da Fatura-Duplicata',
  `SE1_CodigoMovimento` varchar(2) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'PagFor',
  `SE1_InstrucaoCheque` varchar(40) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'PagFor - Instrucao cheque OP',
  `SE1_TipoDocCompeTed` varchar(1) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'PagFor - C=Titularidade diferente D=Mesma Titularidade',
  `SE1_TipoContaModalidade` varchar(2) COLLATE latin1_general_ci DEFAULT NULL COMMENT 'PagFor - Tipo de Conta Modalidade DOC/COMPE/TED',
  `SE1_CodigoLancamento` int DEFAULT NULL COMMENT 'PagFor - Codigo de Lancamento',
  `SE1_BoletoEnviadoEmail` int NOT NULL DEFAULT '0' COMMENT '0 - Boleto não enviado por e-mail\n1- Boleto Enviado por e-mail',
  `SE1_TaxaJuros` decimal(9,6) DEFAULT '0.000000',
  `SE1_ValorJuros` decimal(11,2) DEFAULT '0.00',
  `SE1_ValorArrecadado` decimal(17,2) DEFAULT NULL COMMENT 'Valor arrecadado SisPag',
  PRIMARY KEY (`SE1_ID`),
  KEY `SE1_TabOrigem` (`SE1_IDEA1`),
  KEY `FK_SE1_SF1` (`SE1_IDSF1`),
  KEY `FK_SE1_SE3` (`SE1_IDSE3`),
  KEY `FK_SE1_IE2` (`SE1_IDIE2`),
  KEY `FK_SE1_DR1` (`SE1_IDDR1`),
  KEY `FK_SE1_SA6` (`SE1_IDSA6`),
  KEY `FK_SE1_TipoPagamento` (`SE1_IDTipoPagamento`),
  KEY `FK_SE1_FormaPagamento` (`SE1_IDFormaPagamento`),
  KEY `FK_SE1_SE1` (`SE1_IDRef`),
  KEY `FK_SE1_SEP` (`SE1_IDSEP`),
  KEY `SE1_IDSEA` (`SE1_IDSEA`),
  KEY `SE1_IDSEARef` (`SE1_IDSEARef`),
  KEY `FK_SE1_SE6` (`SE1_IDSE6`),
  KEY `IDEA1_Vencto` (`SE1_IDEA1`,`SE1_Vencto`),
  KEY `IDSE7` (`SE1_IDSE7`),
  KEY `FK_SE1_IDSA8` (`SE1_IDSA8`)
);
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('IE1');
    }
};
