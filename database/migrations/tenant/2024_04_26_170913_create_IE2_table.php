<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
CREATE TABLE `IE2` (
  `IE2_ID` int NOT NULL AUTO_INCREMENT,
  `IE2_IDEA1` int NOT NULL COMMENT 'Id da Empresa',
  `IE2_Tipo` varchar(1) COLLATE latin1_general_ci NOT NULL COMMENT 'Tipo',
  `IE2_IDIE1` int NOT NULL COMMENT 'Id do cabeçalho de Investimento / empréstimo',
  `IE2_IDSA1` int DEFAULT '0' COMMENT 'Fornecedor (no caso, por exemplo, de compra de equipamentos)',
  `IE2_Doc` varchar(20) COLLATE latin1_general_ci DEFAULT '',
  `IE2_Hist` varchar(255) COLLATE latin1_general_ci NOT NULL COMMENT 'Histï¿½rico',
  `IE2_Valor` decimal(17,2) DEFAULT NULL COMMENT 'Valor',
  `IE2_Emissao` datetime NOT NULL COMMENT 'Data de emissão',
  `IE2_CRIADOR` int unsigned NOT NULL COMMENT 'Id do Criador',
  `IE2_ALTERADOR` int unsigned NOT NULL DEFAULT '0' COMMENT 'Id do último alterador',
  `IE2_DT_INC` datetime NOT NULL COMMENT 'Data de inclusão do registro',
  `IE2_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteração do registro',
  `IE2_STATUS` int NOT NULL DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
  PRIMARY KEY (`IE2_ID`),
  KEY `IE2_Emissao` (`IE2_IDEA1`,`IE2_Emissao`),
  KEY `FK_IE2_IE1` (`IE2_IDIE1`)
);
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('IE2');
    }
};
