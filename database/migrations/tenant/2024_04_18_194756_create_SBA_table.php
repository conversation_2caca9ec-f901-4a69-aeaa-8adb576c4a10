<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
CREATE TABLE `SBA` (
  `SBA_ID` int NOT NULL AUTO_INCREMENT,
  `SBA_IDPAI` int DEFAULT '0' COMMENT 'ID do registro da Categoria',
  `SBA_IDEA1` int NOT NULL COMMENT 'Id da empresa',
  `SBA_Tipo` varchar(16) COLLATE latin1_general_ci NOT NULL COMMENT 'Tipo do produto (categoria)',
  `SBA_Desc` varchar(255) COLLATE latin1_general_ci DEFAULT NULL,
  `SBA_EnviadoZanthus` varchar(1) COLLATE latin1_general_ci DEFAULT 'N' COMMENT 'Indica se foi enviado ou nao ao Sistema Zanthus',
  `SBA_AutomComercial` varchar(1) COLLATE latin1_general_ci DEFAULT 'N' COMMENT 'Usado no sistema de Automacao Comercial',
  `SBA_AjustaPeso` varchar(1) COLLATE latin1_general_ci DEFAULT 'N' COMMENT 'Indica se permite ajustar o Peso Teorico para Peso Real',
  `SBA_Ativo` int DEFAULT '1' COMMENT '1=Ativo, 0=Inativo',
  `SBA_CRIADOR` int unsigned NOT NULL COMMENT 'Id do Criador',
  `SBA_ALTERADOR` int unsigned NOT NULL DEFAULT '0' COMMENT 'Id do último alterador',
  `SBA_DT_INC` datetime NOT NULL COMMENT 'Data de inclusão do registro',
  `SBA_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteração do registro',
  `SBA_STATUS` int NOT NULL DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
  PRIMARY KEY (`SBA_ID`),
  KEY `SBA_Desc` (`SBA_IDEA1`,`SBA_Desc`,`SBA_STATUS`)
);
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('SBA');
    }
};
