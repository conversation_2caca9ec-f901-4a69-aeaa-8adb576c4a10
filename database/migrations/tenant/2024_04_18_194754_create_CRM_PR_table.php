<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
        CREATE TABLE `CRM_PR` (
          `CRM_PR_ID` int NOT NULL AUTO_INCREMENT,
          `CRM_PR_IDEA1` int NOT NULL,
          `CRM_PR_OP` int NOT NULL,
          `CRM_PR_TP` int NOT NULL DEFAULT '0',
          `CRM_PR_DOC` varchar(255) CHARACTER SET latin1 NOT NULL,
          `CRM_PR_EMP` int NOT NULL,
          `CRM_PR_EMP_CT` int NOT NULL,
          `CRM_PR_PS` longtext CHARACTER SET latin1,
          `CRM_PR_MRR` longtext CHARACTER SET latin1,
          `CRM_PR_OBS` longtext CHARACTER SET latin1,
          `CRM_PR_VERSAO` int NOT NULL DEFAULT '1',
          `CRM_PR_DT_INC` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
          `CRM_PR_OWNER` int NOT NULL,
          `CRM_PR_VALID` date NOT NULL,
          `CRM_PR_MODELO` int DEFAULT '0',
          `CRM_PR_STATUS` int DEFAULT '0',
          `CRM_PR_KEY` varchar(255) COLLATE latin1_general_ci DEFAULT NULL,
          `CRM_PR_UID` char(32) COLLATE latin1_general_ci DEFAULT NULL,
          PRIMARY KEY (`CRM_PR_ID`),
          KEY `CRM_PR_OP` (`CRM_PR_IDEA1`,`CRM_PR_OP`,`CRM_PR_TP`),
          KEY `CRM_PR_UID` (`CRM_PR_UID`)
        )
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('CRM_PR');
    }
};
