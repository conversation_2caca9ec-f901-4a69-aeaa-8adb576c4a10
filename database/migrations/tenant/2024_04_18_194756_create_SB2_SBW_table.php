<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
CREATE TABLE `SB2_SBW` (
  `SB2_SBW_ID` int NOT NULL AUTO_INCREMENT,
  `SB2_SBW_IDEA1` int DEFAULT '0' COMMENT 'Id da empresa',
  `SB2_SBW_IDSBW` int DEFAULT '0' COMMENT 'ID do Armazem',
  `SB2_SBW_IDSB1` int DEFAULT '0',
  `SB2_SBW_IDSB2` int DEFAULT '0',
  `SB2_SBW_QAnt` decimal(10,3) DEFAULT '0.000',
  `SB2_SBW_QAtu` decimal(10,3) DEFAULT '0.000',
  `SB2_SBW_CRIADOR` int unsigned NOT NULL DEFAULT '0' COMMENT 'Id do Criador',
  `SB2_SBW_ALTERADOR` int unsigned NOT NULL DEFAULT '0' COMMENT 'Id do Ãºltimo alterador',
  `SB2_SBW_DT_INC` datetime NOT NULL  COMMENT 'Data de inclusÃ£o do registro',
  `SB2_SBW_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteraÃ§Ã£o do registro',
  `SB2_SBW_STATUS` int NOT NULL DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
  PRIMARY KEY (`SB2_SBW_ID`),
  UNIQUE KEY `IDSBW_IDSB2` (`SB2_SBW_IDSBW`,`SB2_SBW_IDSB2`),
  KEY `IDEA1` (`SB2_SBW_IDEA1`),
  KEY `IDSB1` (`SB2_SBW_IDSB1`),
  KEY `IDSB2` (`SB2_SBW_IDSB2`)
);
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('SB2_SBW');
    }
};
