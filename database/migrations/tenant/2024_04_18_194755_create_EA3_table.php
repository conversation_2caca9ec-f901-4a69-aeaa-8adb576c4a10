<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
CREATE TABLE `EA3` (
  `EA3_ID` int NOT NULL AUTO_INCREMENT,
  `EA3_IDEA1` int NOT NULL COMMENT 'Id da Empresa',
  `EA3_IDEA2` int NOT NULL COMMENT 'Id do Usuário',
  `EA3_IDMN2` int NOT NULL DEFAULT '0' COMMENT 'Id do Perfil',
  `EA3_CRIADOR` int unsigned NOT NULL COMMENT 'Id do Criador',
  `EA3_ALTERADOR` int unsigned NOT NULL DEFAULT '0' COMMENT 'Id do último alterador',
  `EA3_DT_INC` datetime NOT NULL COMMENT 'Data de inclusão do registro',
  `EA3_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteração do registro',
  `EA3_STATUS` int NOT NULL DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
  PRIMARY KEY (`EA3_ID`),
  KEY `EA3_EA1` (`EA3_IDEA1`),
  KEY `EA3_EA2` (`EA3_IDEA2`),
  KEY `FK_EA3_MN2` (`EA3_IDMN2`)
);
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('EA3');
    }
};
