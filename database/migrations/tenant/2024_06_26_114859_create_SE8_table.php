<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
        CREATE TABLE `SE8` (
  `SE8_ID` int NOT NULL AUTO_INCREMENT,
  `SE8_IDEA1` int NOT NULL ,
  `SE8_IDSE1_Orig` int DEFAULT NULL ,
  `SE8_IDSE2_Orig` int DEFAULT NULL ,
  `SE8_IDSE5_Orig` int DEFAULT NULL ,
  `SE8_IDSE1_Dest` int DEFAULT NULL ,
  `SE8_IDSE2_Dest` int DEFAULT NULL ,
  `SE8_IDSE5_Dest` int DEFAULT NULL ,
  `SE8_CRIADOR` int unsigned DEFAULT NULL ,
  `SE8_ALTERADOR` int unsigned DEFAULT '0' ,
  `SE8_DT_INC` datetime DEFAULT NULL ,
  `SE8_DT_ALT` datetime DEFAULT NULL ,
  `SE8_STATUS` int DEFAULT '0' ,
  <PERSON><PERSON>AR<PERSON> KEY (`SE8_ID`)
          )
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('SE8');
    }
};
