<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
        CREATE TABLE `spx38940017` (
            `id` int NOT NULL AUTO_INCREMENT,
            `link` int DEFAULT NULL,
            `cpo001` double DEFAULT NULL,
            `cpo002` double DEFAULT NULL,
            `cpo003` int DEFAULT NULL,
            `cpo004` int DEFAULT NULL,
            `cpo005` int DEFAULT NULL,
            `cpo006` varchar(15) COLLATE latin1_general_ci DEFAULT NULL,
            `cpo007` int DEFAULT NULL,
            `cpo008` int DEFAULT NULL,
            `cpo009` varchar(15) COLLATE latin1_general_ci DEFAULT NULL,
            `cpo010` date DEFAULT NULL,
            `cpo011` varchar(15) COLLATE latin1_general_ci DEFAULT NULL,
            `cpo012` longtext COLLATE latin1_general_ci,
            `cpo013` int DEFAULT NULL,
            `cpo014` int DEFAULT NULL,
            `cpo015` longtext COLLATE latin1_general_ci,
            `cpo016` longtext COLLATE latin1_general_ci,
            `cpo017` longtext COLLATE latin1_general_ci,
            `cpo018` double DEFAULT NULL,
            `cpo019` double DEFAULT NULL,
            `cpo020` varchar(15) COLLATE latin1_general_ci DEFAULT NULL,
            `cpo021` varchar(15) COLLATE latin1_general_ci DEFAULT NULL,
            `cpo022` int DEFAULT NULL,
            `cpo023` int DEFAULT NULL,
            `cpo024` date DEFAULT NULL,
            `cpo025` int DEFAULT NULL,
            `cpo027` longblob,
            `cpo028` longblob,
            `cpo029` int DEFAULT NULL,
            `cpo030` int DEFAULT NULL,
            `cpo031` longtext COLLATE latin1_general_ci,
            `cpo032` longtext COLLATE latin1_general_ci,
            `cpo033` int DEFAULT NULL,
            `cpo034` date DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `link` (`link`)
          )
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('spx38940012');
    }
};
