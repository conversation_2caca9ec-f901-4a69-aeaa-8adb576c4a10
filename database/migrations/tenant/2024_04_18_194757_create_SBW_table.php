<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
CREATE TABLE `SBW` (
  `SBW_ID` int NOT NULL AUTO_INCREMENT,
  `SBW_IDEA1` int NOT NULL COMMENT 'Id da Empresa',
  `SBW_IDSA1` int DEFAULT NULL COMMENT 'ID do Terceiro',
  `SBW_Tipo` varchar(1) COLLATE latin1_general_ci DEFAULT '0' COMMENT '0-proprio: nosso produto em nosso poder/produto de terceiro em nosso poder; 1-terceiro: nosso produto em poder de terceiro',
  `SBW_Desc` varchar(60) COLLATE latin1_general_ci NOT NULL COMMENT 'DescriÃ§Ã£o',
  `SBW_DisponivelVenda` varchar(1) COLLATE latin1_general_ci DEFAULT '1' COMMENT '''0''-Nao disponivel para Venda, ''1''-Disponivel',
  `SBW_CRIADOR` int unsigned DEFAULT NULL COMMENT 'Id do Criador',
  `SBW_ALTERADOR` int unsigned DEFAULT '0' COMMENT 'Id do Ãºltimo alterador',
  `SBW_DT_INC` datetime DEFAULT NULL COMMENT 'Data de inclusÃ£o do registro',
  `SBW_DT_ALT` datetime DEFAULT NULL COMMENT 'Data de alteraÃ§Ã£o do registro',
  `SBW_STATUS` int DEFAULT '0' COMMENT 'Status (0=ativo, 1=deletado, 2=bloqueado)',
  PRIMARY KEY (`SBW_ID`),
  KEY `SBW_IDEA1` (`SBW_IDEA1`,`SBW_Desc`),
  KEY `SBW_IDSA1` (`SBW_IDSA1`),
  KEY `IDSA1_Tipo` (`SBW_IDSA1`,`SBW_Tipo`)
);
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('SBW');
    }
};
